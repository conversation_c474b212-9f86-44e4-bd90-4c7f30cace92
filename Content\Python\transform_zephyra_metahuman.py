#!/usr/bin/env python3
"""
🔥 TRANSFORMAR METAHUMAN EM ZEPHYRA 🔥
====================================

Script específico para transformar o MetaHuman criado pelo usuário
em Zephyra, a Tecelã dos Realms, aplicando todos os assets e modificações.

MetaHuman Path: /Game/Champions/Zephyra/Zephyra.Zephyra
"""

import unreal
import json

class ZephyraTransformer:
    """Transformador do MetaHuman em Zephyra"""
    
    def __init__(self):
        """Inicializar"""
        self.editor_subsystem = unreal.get_editor_subsystem(unreal.UnrealEditorSubsystem)
        self.world = self.editor_subsystem.get_editor_world()
        
        print("🔥 ZEPHYRA TRANSFORMER INICIALIZADO")
        print(f"✅ Mundo: {self.world.get_name()}")
    
    def transform_metahuman_to_zephyra(self):
        """Transformar MetaHuman em Zephyra completa"""
        try:
            print("🌟 === TRANSFORMANDO METAHUMAN EM ZEPHYRA ===")
            
            # 1. Carregar MetaHuman da Zephyra
            zephyra_metahuman = self._load_zephyra_metahuman()
            if not zephyra_metahuman:
                return False
            
            # 2. Carregar todos os assets já criados
            assets = self._load_all_zephyra_assets()
            
            # 3. Spawnar MetaHuman no mundo
            zephyra_actor = self._spawn_zephyra_metahuman(zephyra_metahuman)
            if not zephyra_actor:
                return False
            
            # 4. Aplicar todas as modificações
            self._apply_all_modifications(zephyra_actor, assets)
            
            # 5. Configurar habilidades
            self._setup_zephyra_abilities(zephyra_actor)
            
            # 6. Salvar configuração final
            self._save_final_configuration(zephyra_actor, assets)
            
            print("🎉 === ZEPHYRA TRANSFORMADA COM SUCESSO! ===")
            print(f"📍 Ator: {zephyra_actor.get_actor_label()}")
            print(f"📍 Localização: {zephyra_actor.get_actor_location()}")
            print("🔥 METAHUMAN AGORA É A ZEPHYRA!")
            
            return True
            
        except Exception as e:
            print(f"❌ ERRO: {str(e)}")
            return False
    
    def _load_zephyra_metahuman(self):
        """Carregar o MetaHuman da Zephyra"""
        print("👤 Carregando MetaHuman da Zephyra...")
        
        # Caminhos possíveis para o MetaHuman
        possible_paths = [
            "/Game/Champions/Zephyra/Zephyra",
            "/Game/Champions/Zephyra/Zephyra.Zephyra",
            "/Game/Champions/Zephyra/BP_Zephyra"
        ]
        
        for path in possible_paths:
            try:
                print(f"🔍 Tentando carregar: {path}")
                metahuman = unreal.EditorAssetLibrary.load_asset(path)
                if metahuman:
                    print(f"✅ MetaHuman carregado: {path}")
                    print(f"✅ Tipo: {type(metahuman).__name__}")
                    return metahuman
            except Exception as e:
                print(f"⚠️ Erro em {path}: {str(e)}")
                continue
        
        # Procurar em toda a pasta
        print("🔍 Procurando em toda a pasta Zephyra...")
        try:
            all_assets = unreal.EditorAssetLibrary.list_assets("/Game/Champions/Zephyra/", recursive=True)
            for asset_path in all_assets:
                if 'zephyra' in asset_path.lower():
                    try:
                        asset = unreal.EditorAssetLibrary.load_asset(asset_path)
                        if asset and (hasattr(asset, 'generated_class') or isinstance(asset, unreal.Blueprint)):
                            print(f"✅ MetaHuman encontrado: {asset_path}")
                            return asset
                    except:
                        continue
        except Exception as e:
            print(f"⚠️ Erro na busca: {str(e)}")
        
        print("❌ MetaHuman da Zephyra não encontrado!")
        return None
    
    def _load_all_zephyra_assets(self):
        """Carregar todos os assets da Zephyra já criados"""
        print("📦 Carregando assets da Zephyra...")
        
        assets = {
            'materials': {},
            'particle_systems': {},
            'audio_assets': {},
            'textures': {}
        }
        
        # Carregar Materials
        material_paths = [
            "/Game/Champions/Zephyra/Materials/M_Zephyra_PrismalBody",
            "/Game/Champions/Zephyra/Materials/M_Zephyra_EtherealHair",
            "/Game/Champions/Zephyra/Materials/M_Zephyra_DimensionalEyes"
        ]
        
        for path in material_paths:
            try:
                material = unreal.EditorAssetLibrary.load_asset(path)
                if material:
                    name = path.split('/')[-1]
                    assets['materials'][name] = material
                    print(f"✅ Material: {name}")
            except:
                pass
        
        # Carregar Particle Systems
        ps_paths = [
            "/Game/Champions/Zephyra/VFX/NS_Zephyra_DimensionalAura",
            "/Game/Champions/Zephyra/VFX/NS_Zephyra_AbilityEffects"
        ]
        
        for path in ps_paths:
            try:
                ps = unreal.EditorAssetLibrary.load_asset(path)
                if ps:
                    name = path.split('/')[-1]
                    assets['particle_systems'][name] = ps
                    print(f"✅ Particle System: {name}")
            except:
                pass
        
        # Carregar Audio Assets
        audio_paths = [
            "/Game/Champions/Zephyra/Audio/SC_Zephyra_Abilities",
            "/Game/Champions/Zephyra/Audio/SC_Zephyra_DimensionalAmbient"
        ]
        
        for path in audio_paths:
            try:
                audio = unreal.EditorAssetLibrary.load_asset(path)
                if audio:
                    name = path.split('/')[-1]
                    assets['audio_assets'][name] = audio
                    print(f"✅ Audio Asset: {name}")
            except:
                pass
        
        total_assets = sum(len(category) for category in assets.values())
        print(f"✅ Total de assets carregados: {total_assets}")
        
        return assets
    
    def _spawn_zephyra_metahuman(self, metahuman):
        """Spawnar MetaHuman da Zephyra no mundo"""
        print("🌍 Spawnando Zephyra no mundo...")
        
        try:
            # Determinar classe do ator
            if hasattr(metahuman, 'generated_class'):
                actor_class = metahuman.generated_class()
                print(f"✅ Usando Blueprint class: {actor_class}")
            elif isinstance(metahuman, unreal.Blueprint):
                actor_class = metahuman.generated_class()
                print(f"✅ Usando Blueprint: {actor_class}")
            else:
                actor_class = unreal.Character
                print("⚠️ Usando Character como fallback")
            
            # Spawnar ator
            spawn_location = unreal.Vector(500, 0, 100)  # Posição específica para Zephyra
            zephyra_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
                actor_class,
                spawn_location
            )
            
            if zephyra_actor:
                # Configurar ator
                zephyra_actor.set_actor_label("ZEPHYRA_REALM_WEAVER_FINAL")
                zephyra_actor.set_actor_scale3d(unreal.Vector(1.05, 1.05, 1.08))  # Escala etérea
                
                # Tags especiais
                zephyra_actor.tags = [
                    unreal.Name("Zephyra"),
                    unreal.Name("RealmWeaver"),
                    unreal.Name("MetaHuman"),
                    unreal.Name("Champion"),
                    unreal.Name("Mythic"),
                    unreal.Name("Support"),
                    unreal.Name("Dimensional"),
                    unreal.Name("FINAL")
                ]
                
                print(f"✅ Zephyra spawnada: {zephyra_actor.get_actor_label()}")
                return zephyra_actor
            else:
                print("❌ Falha ao spawnar Zephyra")
                return None
                
        except Exception as e:
            print(f"❌ Erro ao spawnar: {str(e)}")
            return None
    
    def _apply_all_modifications(self, actor, assets):
        """Aplicar todas as modificações da Zephyra"""
        print("⚡ Aplicando modificações da Zephyra...")
        
        try:
            # 1. Aplicar materials se possível
            if assets['materials']:
                self._apply_materials_to_actor(actor, assets['materials'])
            
            # 2. Adicionar efeitos de partículas
            if assets['particle_systems']:
                self._add_particle_effects(actor, assets['particle_systems'])
            
            # 3. Configurar audio
            if assets['audio_assets']:
                self._setup_audio_components(actor, assets['audio_assets'])
            
            # 4. Configurar propriedades especiais
            self._configure_special_properties(actor)
            
            print("✅ Todas as modificações aplicadas")
            
        except Exception as e:
            print(f"❌ Erro ao aplicar modificações: {str(e)}")
    
    def _apply_materials_to_actor(self, actor, materials):
        """Aplicar materials ao ator"""
        print("🎨 Aplicando materials...")
        
        try:
            # Tentar encontrar componentes de mesh
            mesh_components = actor.get_components_by_class(unreal.SkeletalMeshComponent)
            
            for i, mesh_comp in enumerate(mesh_components):
                if mesh_comp and 'M_Zephyra_PrismalBody' in materials:
                    try:
                        mesh_comp.set_material(0, materials['M_Zephyra_PrismalBody'])
                        print(f"✅ Material prismal aplicado ao componente {i}")
                    except Exception as e:
                        print(f"⚠️ Erro ao aplicar material: {str(e)}")
                        
        except Exception as e:
            print(f"⚠️ Erro ao aplicar materials: {str(e)}")
    
    def _add_particle_effects(self, actor, particle_systems):
        """Adicionar efeitos de partículas"""
        print("✨ Adicionando efeitos de partículas...")
        
        try:
            # Adicionar aura dimensional
            if 'NS_Zephyra_DimensionalAura' in particle_systems:
                try:
                    particle_comp = actor.add_component_by_class(unreal.NiagaraComponent)
                    if particle_comp:
                        particle_comp.set_asset(particle_systems['NS_Zephyra_DimensionalAura'])
                        particle_comp.set_component_label("DimensionalAura")
                        particle_comp.set_auto_activate(True)
                        print("✅ Aura dimensional adicionada")
                except Exception as e:
                    print(f"⚠️ Erro na aura dimensional: {str(e)}")
            
            # Adicionar efeitos de habilidades
            if 'NS_Zephyra_AbilityEffects' in particle_systems:
                try:
                    ability_comp = actor.add_component_by_class(unreal.NiagaraComponent)
                    if ability_comp:
                        ability_comp.set_asset(particle_systems['NS_Zephyra_AbilityEffects'])
                        ability_comp.set_component_label("AbilityEffects")
                        ability_comp.set_auto_activate(False)  # Ativar apenas durante habilidades
                        print("✅ Efeitos de habilidades adicionados")
                except Exception as e:
                    print(f"⚠️ Erro nos efeitos de habilidades: {str(e)}")
                    
        except Exception as e:
            print(f"⚠️ Erro ao adicionar efeitos: {str(e)}")
    
    def _setup_audio_components(self, actor, audio_assets):
        """Configurar componentes de audio"""
        print("🔊 Configurando audio...")
        
        try:
            # Adicionar audio de habilidades
            if 'SC_Zephyra_Abilities' in audio_assets:
                try:
                    audio_comp = actor.add_component_by_class(unreal.AudioComponent)
                    if audio_comp:
                        audio_comp.set_sound(audio_assets['SC_Zephyra_Abilities'])
                        audio_comp.set_component_label("AbilitiesAudio")
                        audio_comp.set_auto_activate(False)
                        print("✅ Audio de habilidades configurado")
                except Exception as e:
                    print(f"⚠️ Erro no audio de habilidades: {str(e)}")
            
            # Adicionar audio ambiente
            if 'SC_Zephyra_DimensionalAmbient' in audio_assets:
                try:
                    ambient_comp = actor.add_component_by_class(unreal.AudioComponent)
                    if ambient_comp:
                        ambient_comp.set_sound(audio_assets['SC_Zephyra_DimensionalAmbient'])
                        ambient_comp.set_component_label("AmbientAudio")
                        ambient_comp.set_auto_activate(True)
                        ambient_comp.set_volume_multiplier(0.3)  # Volume baixo para ambiente
                        print("✅ Audio ambiente configurado")
                except Exception as e:
                    print(f"⚠️ Erro no audio ambiente: {str(e)}")
                    
        except Exception as e:
            print(f"⚠️ Erro ao configurar audio: {str(e)}")
    
    def _configure_special_properties(self, actor):
        """Configurar propriedades especiais da Zephyra"""
        print("⚙️ Configurando propriedades especiais...")
        
        try:
            # Configurar movimento etéreo (se possível)
            movement_comp = actor.get_component_by_class(unreal.CharacterMovementComponent)
            if movement_comp:
                try:
                    movement_comp.set_editor_property("max_walk_speed", 340.0)  # Velocidade da Zephyra
                    movement_comp.set_editor_property("gravity_scale", 0.9)  # Ligeiramente menos gravidade
                    print("✅ Movimento etéreo configurado")
                except Exception as e:
                    print(f"⚠️ Erro no movimento: {str(e)}")
            
        except Exception as e:
            print(f"⚠️ Erro nas propriedades especiais: {str(e)}")
    
    def _setup_zephyra_abilities(self, actor):
        """Configurar habilidades da Zephyra"""
        print("⚡ Configurando habilidades da Zephyra...")
        
        abilities = {
            "Passive": "Maestria Dimensional",
            "Q": "Tecer Trilho", 
            "W": "Ponte Vertical",
            "E": "Redirecionamento Prismal",
            "R": "Convergência dos Realms"
        }
        
        print("✅ Habilidades da Zephyra:")
        for key, ability in abilities.items():
            print(f"   {key}: {ability}")
    
    def _save_final_configuration(self, actor, assets):
        """Salvar configuração final"""
        print("💾 Salvando configuração final...")
        
        try:
            config = {
                "zephyra_final": {
                    "actor_name": actor.get_actor_label(),
                    "actor_location": {
                        "x": float(actor.get_actor_location().x),
                        "y": float(actor.get_actor_location().y),
                        "z": float(actor.get_actor_location().z)
                    },
                    "assets_applied": {
                        "materials": len(assets['materials']),
                        "particle_systems": len(assets['particle_systems']),
                        "audio_assets": len(assets['audio_assets'])
                    },
                    "status": "PRODUCTION_READY",
                    "transformation_complete": True
                }
            }
            
            # Salvar JSON
            json_path = "C:/Aura/projeto/Auracron/Content/Champions/Zephyra/Data/zephyra_final_config.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print("✅ Configuração final salva")
            
        except Exception as e:
            print(f"⚠️ Erro ao salvar configuração: {str(e)}")


def transform_zephyra_metahuman():
    """Função principal"""
    try:
        print("🔥 === INICIANDO TRANSFORMAÇÃO ===")
        
        transformer = ZephyraTransformer()
        success = transformer.transform_metahuman_to_zephyra()
        
        if success:
            print("🎉 === TRANSFORMAÇÃO COMPLETA! ===")
            print("🔥 METAHUMAN AGORA É ZEPHYRA!")
            print("🌟 A Tecelã dos Realms está pronta!")
            print("📍 Verifique o World Outliner: ZEPHYRA_REALM_WEAVER_FINAL")
            return True
        else:
            print("❌ FALHA NA TRANSFORMAÇÃO")
            return False
            
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        return False

def main():
    """Função principal"""
    return transform_zephyra_metahuman()

# Executar automaticamente
if __name__ == "__main__":
    success = main()
    if success:
        print("🔥 SUCESSO! ZEPHYRA TRANSFORMADA!")
    else:
        print("💀 FALHOU!")
else:
    print("🔥 Zephyra Transformer carregado!")
    print("💡 Execute: transform_zephyra_metahuman()")
