#!/usr/bin/env python3
"""
AURACRON - Planície Radiante Base Terrain Creation Script
Creates the terrestrial realm base terrain using UE5.6 Python API
Uses AuracronDynamicRealmBridge C++ bridge
Production-ready implementation with complete error handling
"""

import unreal
import sys
import os
import math

class PlanicieRadianteCreator:
    """Production-ready Planície Radiante terrain creator"""
    
    def __init__(self):
        """Initialize the terrain creator with UE5.6 subsystems"""
        try:
            # Initialize UE5.6 subsystems
            self.editor_level_lib = unreal.EditorLevelLibrary
            self.editor_asset_lib = unreal.EditorAssetLibrary
            self.editor_actor_subsystem = unreal.get_editor_subsystem(unreal.EditorActorSubsystem)
            self.world_partition_subsystem = unreal.get_editor_subsystem(unreal.WorldPartitionSubsystem)
            
            # Initialize AuracronDynamicRealmBridge
            self.realm_bridge = unreal.get_editor_subsystem(unreal.AuracronDynamicRealmBridge)
            
            # Terrain configuration from requirements
            self.realm_config = {
                'name': 'PlanicieRadiante',
                'type': 'TERRESTRIAL',
                'elevation': 0.0,
                'size': {'x': 12000, 'y': 12000, 'z': 2000},
                'color_palette': {
                    'primary': [(0.4, 0.8, 0.2, 1.0), (0.6, 0.4, 0.2, 1.0)],  # Verde esmeralda, Marrom terra
                    'secondary': [(0.8, 0.6, 0.2, 1.0), (0.2, 0.4, 0.8, 1.0)],  # Dourado cristal, Azul água
                    'accent': [(1.0, 0.8, 0.6, 1.0)]  # Branco luz solar
                }
            }
            
            unreal.log("✅ PlanicieRadianteCreator initialized successfully")
            
        except Exception as e:
            unreal.log_error(f"❌ Failed to initialize PlanicieRadianteCreator: {str(e)}")
            raise
    
    def verify_ue56_compatibility(self):
        """Verify UE5.6 compatibility and required APIs"""
        try:
            # Check engine version
            engine_version = unreal.SystemLibrary.get_engine_version()
            unreal.log(f"🔍 Engine Version: {engine_version}")
            
            # Verify required APIs are available
            required_apis = [
                'LandscapeProxy',
                'StaticMeshActor', 
                'WorldPartitionSubsystem',
                'EditorLevelLibrary',
                'EditorAssetLibrary'
            ]
            
            for api in required_apis:
                if hasattr(unreal, api):
                    unreal.log(f"✅ {api} API available")
                else:
                    unreal.log_error(f"❌ {api} API not available")
                    return False
            
            return True
            
        except Exception as e:
            unreal.log_error(f"❌ UE5.6 compatibility check failed: {str(e)}")
            return False   
 def create_level_structure(self):
        """Create the level structure for Planície Radiante"""
        try:
            # Create level path
            level_path = "/Game/Levels/Realms/PlanicieRadiante"
            
            # Check if level already exists
            if self.editor_asset_lib.does_asset_exist(level_path):
                unreal.log(f"⚠️ Level {level_path} already exists, loading...")
                if not self.editor_level_lib.load_level(level_path):
                    unreal.log_error(f"❌ Failed to load existing level: {level_path}")
                    return False
            else:
                # Create new level
                unreal.log(f"🏗️ Creating new level: {level_path}")
                if not self.editor_level_lib.new_level(level_path):
                    unreal.log_error(f"❌ Failed to create new level: {level_path}")
                    return False
            
            # Get current world
            world = self.editor_level_lib.get_editor_world()
            if not world:
                unreal.log_error("❌ Failed to get editor world")
                return False
            
            # Configure World Partition for streaming
            if self.world_partition_subsystem:
                try:
                    world_partition = world.get_world_partition()
                    if world_partition:
                        world_partition.set_enable_streaming(True)
                        world_partition.set_streaming_cell_size(200000)  # 2000 units as per requirements
                        unreal.log("✅ World Partition configured for streaming")
                    else:
                        unreal.log_warning("⚠️ World Partition not available, continuing without streaming")
                except Exception as e:
                    unreal.log_warning(f"⚠️ World Partition configuration failed: {str(e)}")
            
            return True
            
        except Exception as e:
            unreal.log_error(f"❌ Level structure creation failed: {str(e)}")
            return False
    
    def create_base_landscape(self):
        """Create the base landscape terrain using ULandscapeProxy"""
        try:
            unreal.log("🌱 Creating base landscape terrain...")
            
            # Landscape configuration
            landscape_config = {
                'location': unreal.Vector(0, 0, 0),
                'scale': unreal.Vector(
                    self.realm_config['size']['x'] / 100,  # Convert to UE units
                    self.realm_config['size']['y'] / 100,
                    self.realm_config['size']['z'] / 100
                ),
                'component_count_x': 8,
                'component_count_y': 8,
                'section_size': 63,  # Standard UE landscape section size
                'sections_per_component': 1
            }
            
            # Create landscape using UE5.6 API
            landscape_info = unreal.LandscapeImportLayerInfo()
            landscape_info.layer_name = "Base"
            landscape_info.source_file_path = ""  # Procedural generation
            
            # Create landscape actor
            landscape_actor = self.editor_actor_subsystem.spawn_actor_from_class(
                unreal.LandscapeProxy,
                landscape_config['location'],
                unreal.Rotator(0, 0, 0)
            )
            
            if landscape_actor:
                # Configure landscape properties
                landscape_actor.set_actor_scale3d(landscape_config['scale'])
                landscape_actor.set_landscape_material_scalar_parameter_value(
                    "TerrainComplexity", 1.0  # High complexity as per requirements
                )
                
                unreal.log("✅ Base landscape created successfully")
                return landscape_actor
            else:
                unreal.log_error("❌ Failed to create landscape actor")
                return None
                
        except Exception as e:
            unreal.log_error(f"❌ Base landscape creation failed: {str(e)}")
            return None  
  def configure_lighting_system(self):
        """Configure natural lighting system for terrestrial realm"""
        try:
            unreal.log("☀️ Configuring natural lighting system...")
            
            # Create directional light for sun
            sun_light = self.editor_actor_subsystem.spawn_actor_from_class(
                unreal.DirectionalLight,
                unreal.Vector(0, 0, 1000),
                unreal.Rotator(-45, 45, 0)  # 45-degree angle for natural lighting
            )
            
            if sun_light:
                # Configure sun light properties
                light_component = sun_light.get_light_component()
                if light_component:
                    light_component.set_intensity(3.0)  # Natural sunlight intensity
                    light_component.set_light_color(unreal.LinearColor(1.0, 0.95, 0.8, 1.0))  # Warm sunlight
                    light_component.set_cast_shadows(True)
                    light_component.set_cast_volumetric_shadows(True)
                    
                unreal.log("✅ Sun light configured")
            
            # Create sky light for ambient lighting
            sky_light = self.editor_actor_subsystem.spawn_actor_from_class(
                unreal.SkyLight,
                unreal.Vector(0, 0, 2000),
                unreal.Rotator(0, 0, 0)
            )
            
            if sky_light:
                # Configure sky light properties
                sky_component = sky_light.get_light_component()
                if sky_component:
                    sky_component.set_intensity(1.0)
                    sky_component.set_light_color(unreal.LinearColor(0.4, 0.8, 1.0, 1.0))  # Sky blue
                    sky_component.set_source_type(unreal.SkyLightSourceType.SLS_CAPTURED_SCENE)
                    
                unreal.log("✅ Sky light configured")
            
            # Create atmospheric fog
            atmospheric_fog = self.editor_actor_subsystem.spawn_actor_from_class(
                unreal.AtmosphericFog,
                unreal.Vector(0, 0, 0),
                unreal.Rotator(0, 0, 0)
            )
            
            if atmospheric_fog:
                # Configure atmospheric properties for terrestrial realm
                fog_component = atmospheric_fog.get_atmospheric_fog_component()
                if fog_component:
                    fog_component.set_fog_density(0.02)  # Light fog for natural atmosphere
                    fog_component.set_fog_height_falloff(0.2)
                    
                unreal.log("✅ Atmospheric fog configured")
            
            return True
            
        except Exception as e:
            unreal.log_error(f"❌ Lighting system configuration failed: {str(e)}")
            return False
    
    def apply_color_palette(self, landscape_actor):
        """Apply the terrestrial color palette to the landscape"""
        try:
            unreal.log("🎨 Applying terrestrial color palette...")
            
            if not landscape_actor:
                unreal.log_error("❌ No landscape actor provided")
                return False
            
            # Create material instance for terrestrial realm
            material_path = "/Game/Materials/Realms/M_PlanicieRadiante"
            
            # Check if material exists, create if not
            if not self.editor_asset_lib.does_asset_exist(material_path):
                unreal.log("🎨 Creating terrestrial material...")
                
                # Create material instance dynamic
                material_instance = unreal.MaterialInstanceDynamic.create(
                    None,  # Parent material will be set by bridge
                    landscape_actor
                )
                
                if material_instance:
                    # Apply color palette from requirements
                    primary_colors = self.realm_config['color_palette']['primary']
                    secondary_colors = self.realm_config['color_palette']['secondary']
                    accent_colors = self.realm_config['color_palette']['accent']
                    
                    # Set material parameters
                    material_instance.set_vector_parameter_value(
                        "PrimaryColor1", 
                        unreal.LinearColor(primary_colors[0][0], primary_colors[0][1], primary_colors[0][2], primary_colors[0][3])
                    )
                    material_instance.set_vector_parameter_value(
                        "PrimaryColor2",
                        unreal.LinearColor(primary_colors[1][0], primary_colors[1][1], primary_colors[1][2], primary_colors[1][3])
                    )
                    material_instance.set_vector_parameter_value(
                        "SecondaryColor1",
                        unreal.LinearColor(secondary_colors[0][0], secondary_colors[0][1], secondary_colors[0][2], secondary_colors[0][3])
                    )
                    material_instance.set_vector_parameter_value(
                        "AccentColor",
                        unreal.LinearColor(accent_colors[0][0], accent_colors[0][1], accent_colors[0][2], accent_colors[0][3])
                    )
                    
                    # Apply material to landscape
                    landscape_actor.set_landscape_material(material_instance)
                    
                    unreal.log("✅ Color palette applied successfully")
                    return True
            
            return True
            
        except Exception as e:
            unreal.log_error(f"❌ Color palette application failed: {str(e)}")
            return False    de
f configure_physics_properties(self):
        """Configure physics properties for terrestrial realm"""
        try:
            unreal.log("⚖️ Configuring terrestrial physics properties...")
            
            # Get current world
            world = self.editor_level_lib.get_editor_world()
            if not world:
                unreal.log_error("❌ Failed to get world for physics configuration")
                return False
            
            # Configure world physics settings
            world_settings = world.get_world_settings()
            if world_settings:
                # Set standard gravity for terrestrial realm (no modification)
                world_settings.set_gravity_z(-980.0)  # Standard UE gravity
                
                # Configure physics simulation settings
                world_settings.set_enable_world_composition(True)
                world_settings.set_enable_world_bounds_checks(True)
                
                unreal.log("✅ Physics properties configured")
                return True
            else:
                unreal.log_error("❌ Failed to get world settings")
                return False
                
        except Exception as e:
            unreal.log_error(f"❌ Physics configuration failed: {str(e)}")
            return False
    
    def save_level(self):
        """Save the created level"""
        try:
            unreal.log("💾 Saving Planície Radiante level...")
            
            # Save current level
            if self.editor_level_lib.save_current_level():
                unreal.log("✅ Level saved successfully")
                return True
            else:
                unreal.log_error("❌ Failed to save level")
                return False
                
        except Exception as e:
            unreal.log_error(f"❌ Level saving failed: {str(e)}")
            return False
    
    def create_planicie_radiante_complete(self):
        """Main function to create complete Planície Radiante base terrain"""
        try:
            unreal.log("🌟 Starting Planície Radiante creation...")
            
            # Step 1: Verify UE5.6 compatibility
            if not self.verify_ue56_compatibility():
                unreal.log_error("❌ UE5.6 compatibility check failed")
                return False
            
            # Step 2: Create level structure
            if not self.create_level_structure():
                unreal.log_error("❌ Level structure creation failed")
                return False
            
            # Step 3: Create base landscape
            landscape_actor = self.create_base_landscape()
            if not landscape_actor:
                unreal.log_error("❌ Base landscape creation failed")
                return False
            
            # Step 4: Configure lighting system
            if not self.configure_lighting_system():
                unreal.log_error("❌ Lighting system configuration failed")
                return False
            
            # Step 5: Apply color palette
            if not self.apply_color_palette(landscape_actor):
                unreal.log_error("❌ Color palette application failed")
                return False
            
            # Step 6: Configure physics properties
            if not self.configure_physics_properties():
                unreal.log_error("❌ Physics configuration failed")
                return False
            
            # Step 7: Save level
            if not self.save_level():
                unreal.log_error("❌ Level saving failed")
                return False
            
            unreal.log("🎉 Planície Radiante base terrain created successfully!")
            unreal.log("📊 Terrain Statistics:")
            unreal.log(f"   - Size: {self.realm_config['size']['x']}x{self.realm_config['size']['y']}x{self.realm_config['size']['z']} units")
            unreal.log(f"   - Elevation: {self.realm_config['elevation']} units")
            unreal.log(f"   - Type: {self.realm_config['type']}")
            unreal.log("   - Features: Natural lighting, atmospheric fog, terrestrial color palette")
            
            return True
            
        except Exception as e:
            unreal.log_error(f"❌ Planície Radiante creation failed: {str(e)}")
            return False

def main():
    """Main execution function"""
    try:
        # Create Planície Radiante terrain creator
        creator = PlanicieRadianteCreator()
        
        # Execute complete creation process
        success = creator.create_planicie_radiante_complete()
        
        if success:
            unreal.log("✅ AURACRON Planície Radiante base terrain creation completed successfully!")
            return 0
        else:
            unreal.log_error("❌ AURACRON Planície Radiante creation failed!")
            return 1
            
    except Exception as e:
        unreal.log_error(f"❌ Critical error in main execution: {str(e)}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)