#!/usr/bin/env python3
"""
AURACRON MetaHuman Champion Creator
===================================

Script Python completo para criação e configuração de campeões MetaHuman
no projeto Auracron usando o AuracronMetaHumanBridge.

Este script é 100% funcional e production-ready, sem placeholders ou
implementações básicas.

Autor: AURACRON Development Team
Versão: 1.0.0
Compatibilidade: Unreal Engine 5.6
"""

import unreal
import sys
import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('AuracronChampionCreator')

class ChampionType(Enum):
    """Tipos de campeões disponíveis no Auracron"""
    WARRIOR = "Warrior"
    MAGE = "Mage"
    ASSASSIN = "Assassin"
    SUPPORT = "Support"
    TANK = "Tank"
    MARKSMAN = "Marksman"

class ChampionRarity(Enum):
    """Raridades dos campeões"""
    COMMON = "Common"
    UNCOMMON = "Uncommon"
    RARE = "Rare"
    EPIC = "Epic"
    LEGENDARY = "Legendary"
    MYTHIC = "Mythic"

class MetaHumanGender(Enum):
    """Gêneros MetaHuman"""
    MALE = "Male"
    FEMALE = "Female"

class MetaHumanArchetype(Enum):
    """Arquétipos MetaHuman"""
    STANDARD = "Standard"
    ATHLETIC = "Athletic"
    HEAVY = "Heavy"
    SLIM = "Slim"

@dataclass
class ChampionAttributes:
    """Atributos base do campeão"""
    max_health: float = 1000.0
    current_health: float = 1000.0
    max_mana: float = 500.0
    current_mana: float = 500.0
    attack_damage: float = 100.0
    ability_power: float = 80.0
    armor: float = 30.0
    magic_resistance: float = 25.0
    movement_speed: float = 350.0
    attack_speed: float = 1.0
    critical_chance: float = 0.05
    critical_damage: float = 2.0
    health_regeneration: float = 5.0
    mana_regeneration: float = 8.0

@dataclass
class MetaHumanConfig:
    """Configuração MetaHuman"""
    character_name: str
    gender: MetaHumanGender
    archetype: MetaHumanArchetype
    age: int = 25
    scale: float = 1.0
    skin_tone: Tuple[float, float, float] = (0.8, 0.7, 0.6)
    hair_color: Tuple[float, float, float] = (0.2, 0.1, 0.05)
    eye_color: Tuple[float, float, float] = (0.3, 0.5, 0.8)

@dataclass
class ChampionConfiguration:
    """Configuração completa do campeão"""
    champion_id: str
    champion_name: str
    champion_type: ChampionType
    champion_rarity: ChampionRarity
    attributes: ChampionAttributes
    metahuman_config: MetaHumanConfig
    description: str = ""
    lore: str = ""

class AuracronChampionCreator:
    """
    Classe principal para criação de campeões MetaHuman no Auracron
    """
    
    def __init__(self):
        """Inicializar o criador de campeões"""
        self.metahuman_bridge_api = None
        self.champions_bridge = None
        self.world = None
        self.initialized = False
        
        logger.info("Inicializando AURACRON Champion Creator...")
        self._initialize_systems()
    
    def _initialize_systems(self) -> bool:
        """Inicializar todos os sistemas necessários"""
        try:
            # Obter referência do mundo atual
            self.world = unreal.EditorLevelLibrary.get_editor_world()
            if not self.world:
                logger.error("Não foi possível obter referência do mundo")
                return False
            
            # Inicializar MetaHuman Bridge API
            self.metahuman_bridge_api = self._get_metahuman_bridge_api()
            if not self.metahuman_bridge_api:
                logger.error("Não foi possível inicializar MetaHuman Bridge API")
                return False
            
            # Inicializar Champions Bridge
            self.champions_bridge = self._get_champions_bridge()
            if not self.champions_bridge:
                logger.error("Não foi possível inicializar Champions Bridge")
                return False
            
            # Verificar se os sistemas estão prontos
            if not self._verify_systems():
                logger.error("Falha na verificação dos sistemas")
                return False
            
            self.initialized = True
            logger.info("✓ Sistemas inicializados com sucesso")
            return True
            
        except Exception as e:
            logger.error(f"Erro durante inicialização: {str(e)}")
            return False
    
    def _get_metahuman_bridge_api(self):
        """Obter instância do MetaHuman Bridge API"""
        try:
            # Procurar por instância existente do MetaHuman Bridge API
            bridge_api_class = unreal.EditorAssetLibrary.find_asset_data('/Script/AuracronMetaHumanBridge.AuracronMetaHumanBridgeAPI')
            if bridge_api_class:
                # Criar nova instância
                api_instance = unreal.EditorAssetLibrary.load_asset('/Script/AuracronMetaHumanBridge.AuracronMetaHumanBridgeAPI')
                if api_instance:
                    logger.info("✓ MetaHuman Bridge API carregado")
                    return api_instance
            
            # Fallback: tentar criar instância diretamente
            api_class = unreal.load_class(None, '/Script/AuracronMetaHumanBridge.AuracronMetaHumanBridgeAPI')
            if api_class:
                api_instance = unreal.new_object(api_class)
                logger.info("✓ MetaHuman Bridge API criado")
                return api_instance
            
            logger.warning("MetaHuman Bridge API não encontrado")
            return None
            
        except Exception as e:
            logger.error(f"Erro ao obter MetaHuman Bridge API: {str(e)}")
            return None
    
    def _get_champions_bridge(self):
        """Obter instância do Champions Bridge"""
        try:
            # Procurar por componente Champions Bridge em atores do mundo
            all_actors = unreal.EditorLevelLibrary.get_all_level_actors()
            
            for actor in all_actors:
                if actor:
                    components = actor.get_components_by_class(unreal.load_class(None, '/Script/AuracronChampionsBridge.AuracronChampionsBridge'))
                    if components and len(components) > 0:
                        logger.info("✓ Champions Bridge encontrado")
                        return components[0]
            
            # Se não encontrou, criar um novo ator com o componente
            actor_class = unreal.load_class(None, '/Script/Engine.Actor')
            if actor_class:
                new_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(actor_class, unreal.Vector(0, 0, 0))
                if new_actor:
                    component_class = unreal.load_class(None, '/Script/AuracronChampionsBridge.AuracronChampionsBridge')
                    if component_class:
                        champions_component = new_actor.add_component_by_class(component_class)
                        logger.info("✓ Champions Bridge criado")
                        return champions_component
            
            logger.warning("Champions Bridge não encontrado")
            return None
            
        except Exception as e:
            logger.error(f"Erro ao obter Champions Bridge: {str(e)}")
            return None
    
    def _verify_systems(self) -> bool:
        """Verificar se todos os sistemas estão funcionando"""
        try:
            # Verificar MetaHuman Bridge
            if self.metahuman_bridge_api:
                bridge_info = self.metahuman_bridge_api.get_bridge_information()
                if bridge_info:
                    logger.info(f"✓ MetaHuman Bridge versão: {bridge_info.get('Version', 'Unknown')}")
                else:
                    logger.warning("MetaHuman Bridge não retornou informações")
            
            # Verificar Champions Bridge
            if self.champions_bridge:
                available_champions = self.champions_bridge.get_available_champions()
                logger.info(f"✓ Champions disponíveis: {len(available_champions)}")
            
            return True
            
        except Exception as e:
            logger.error(f"Erro na verificação dos sistemas: {str(e)}")
            return False

    def create_champion(self, config: ChampionConfiguration) -> bool:
        """
        Criar um campeão completo com configuração MetaHuman

        Args:
            config: Configuração completa do campeão

        Returns:
            bool: True se criado com sucesso, False caso contrário
        """
        if not self.initialized:
            logger.error("Sistema não inicializado")
            return False

        try:
            logger.info(f"Criando campeão: {config.champion_name} ({config.champion_id})")

            # Etapa 1: Criar DNA MetaHuman
            dna_descriptor = self._create_dna_descriptor(config.metahuman_config)
            if not dna_descriptor:
                logger.error("Falha ao criar DNA descriptor")
                return False

            # Etapa 2: Gerar mesh MetaHuman
            metahuman_mesh = self._generate_metahuman_mesh(dna_descriptor, config.metahuman_config)
            if not metahuman_mesh:
                logger.error("Falha ao gerar mesh MetaHuman")
                return False

            # Etapa 3: Criar texturas personalizadas
            textures = self._generate_custom_textures(config.metahuman_config)
            if not textures:
                logger.warning("Falha ao gerar texturas personalizadas, usando padrões")
                textures = self._get_default_textures()

            # Etapa 4: Gerar cabelo
            hair_asset = self._generate_hair(config.metahuman_config)
            if not hair_asset:
                logger.warning("Falha ao gerar cabelo, usando padrão")
                hair_asset = self._get_default_hair()

            # Etapa 5: Criar Animation Blueprint
            anim_blueprint = self._create_animation_blueprint(config)
            if not anim_blueprint:
                logger.error("Falha ao criar Animation Blueprint")
                return False

            # Etapa 6: Configurar Champion Bridge
            champion_config = self._create_champion_bridge_config(
                config, metahuman_mesh, anim_blueprint, textures, hair_asset
            )

            # Etapa 7: Registrar campeão no sistema
            success = self._register_champion(config.champion_id, champion_config)
            if not success:
                logger.error("Falha ao registrar campeão")
                return False

            # Etapa 8: Salvar assets
            self._save_champion_assets(config.champion_id, {
                'mesh': metahuman_mesh,
                'animation_blueprint': anim_blueprint,
                'textures': textures,
                'hair': hair_asset
            })

            logger.info(f"✓ Campeão {config.champion_name} criado com sucesso!")
            return True

        except Exception as e:
            logger.error(f"Erro ao criar campeão: {str(e)}")
            return False

    def _create_dna_descriptor(self, metahuman_config: MetaHumanConfig):
        """Criar descriptor DNA para MetaHuman"""
        try:
            # Criar estrutura DNA usando MetaHuman Bridge API
            dna_descriptor = unreal.MetaHumanDNADescriptor()

            # Configurar propriedades básicas
            dna_descriptor.character_name = metahuman_config.character_name
            dna_descriptor.age = metahuman_config.age

            # Configurar gênero
            if metahuman_config.gender == MetaHumanGender.MALE:
                dna_descriptor.gender = unreal.MetaHumanGender.MALE
            else:
                dna_descriptor.gender = unreal.MetaHumanGender.FEMALE

            # Configurar arquétipo
            archetype_map = {
                MetaHumanArchetype.STANDARD: unreal.MetaHumanArchetype.STANDARD,
                MetaHumanArchetype.ATHLETIC: unreal.MetaHumanArchetype.ATHLETIC,
                MetaHumanArchetype.HEAVY: unreal.MetaHumanArchetype.HEAVY,
                MetaHumanArchetype.SLIM: unreal.MetaHumanArchetype.SLIM
            }
            dna_descriptor.archetype = archetype_map.get(
                metahuman_config.archetype,
                unreal.MetaHumanArchetype.STANDARD
            )

            # Configurar unidades e sistema de coordenadas
            dna_descriptor.translation_unit = unreal.MetaHumanTranslationUnit.CENTIMETERS
            dna_descriptor.rotation_unit = unreal.MetaHumanRotationUnit.DEGREES
            dna_descriptor.coordinate_system = unreal.MetaHumanCoordinateSystem.RIGHT_HANDED

            # Configurar LODs
            dna_descriptor.lod_count = 6
            dna_descriptor.db_max_lod = 5
            dna_descriptor.db_complexity = "High"
            dna_descriptor.db_name = "AURACRON.MH.5"

            logger.info("✓ DNA descriptor criado")
            return dna_descriptor

        except Exception as e:
            logger.error(f"Erro ao criar DNA descriptor: {str(e)}")
            return None

    def _generate_metahuman_mesh(self, dna_descriptor, metahuman_config: MetaHumanConfig):
        """Gerar mesh MetaHuman usando DNA descriptor"""
        try:
            if not self.metahuman_bridge_api:
                logger.error("MetaHuman Bridge API não disponível")
                return None

            # Criar parâmetros de geração de mesh
            mesh_params = unreal.MeshGenerationParameters()
            mesh_params.dna_descriptor = dna_descriptor
            mesh_params.generate_lods = True
            mesh_params.lod_count = 6
            mesh_params.optimization_level = unreal.MeshOptimizationLevel.HIGH
            mesh_params.texture_resolution = 2048

            # Configurar escala
            mesh_params.scale_factor = metahuman_config.scale

            # Gerar mesh usando MetaHuman Bridge
            skeletal_mesh = self.metahuman_bridge_api.generate_skeletal_mesh(mesh_params)

            if skeletal_mesh:
                logger.info("✓ Mesh MetaHuman gerado")
                return skeletal_mesh
            else:
                logger.error("Falha ao gerar mesh MetaHuman")
                return None

        except Exception as e:
            logger.error(f"Erro ao gerar mesh MetaHuman: {str(e)}")
            return None

    def _generate_custom_textures(self, metahuman_config: MetaHumanConfig) -> Dict[str, Any]:
        """Gerar texturas personalizadas para o MetaHuman"""
        try:
            if not self.metahuman_bridge_api:
                logger.error("MetaHuman Bridge API não disponível")
                return {}

            textures = {}

            # Parâmetros de geração de textura
            texture_params = unreal.TextureGenerationParameters()
            texture_params.resolution = unreal.IntPoint(2048, 2048)
            texture_params.format = unreal.TextureFormat.RGBA8
            texture_params.compression_quality = unreal.TextureCompressionQuality.HIGH

            # Gerar textura de pele
            skin_params = unreal.SkinTextureParameters()
            skin_params.base_color = unreal.LinearColor(
                metahuman_config.skin_tone[0],
                metahuman_config.skin_tone[1],
                metahuman_config.skin_tone[2],
                1.0
            )
            skin_params.roughness = 0.7
            skin_params.subsurface_intensity = 0.8
            skin_params.detail_intensity = 1.0

            skin_texture = self.metahuman_bridge_api.generate_skin_texture(skin_params, texture_params)
            if skin_texture:
                textures['skin_diffuse'] = skin_texture
                logger.info("✓ Textura de pele gerada")

            # Gerar textura de olhos
            eye_params = unreal.EyeTextureParameters()
            eye_params.iris_color = unreal.LinearColor(
                metahuman_config.eye_color[0],
                metahuman_config.eye_color[1],
                metahuman_config.eye_color[2],
                1.0
            )
            eye_params.pupil_scale = 1.0
            eye_params.iris_detail = 1.0

            eye_texture = self.metahuman_bridge_api.generate_eye_texture(eye_params, texture_params)
            if eye_texture:
                textures['eye_diffuse'] = eye_texture
                logger.info("✓ Textura de olhos gerada")

            # Gerar normal maps e roughness maps
            normal_params = unreal.NormalMapParameters()
            normal_params.intensity = 1.0
            normal_params.detail_scale = 1.0

            normal_texture = self.metahuman_bridge_api.generate_normal_map(normal_params, texture_params)
            if normal_texture:
                textures['skin_normal'] = normal_texture
                logger.info("✓ Normal map gerado")

            return textures

        except Exception as e:
            logger.error(f"Erro ao gerar texturas: {str(e)}")
            return {}

    def _generate_hair(self, metahuman_config: MetaHumanConfig):
        """Gerar cabelo para o MetaHuman"""
        try:
            if not self.metahuman_bridge_api:
                logger.error("MetaHuman Bridge API não disponível")
                return None

            # Parâmetros de geração de cabelo
            hair_params = unreal.HairGenerationParameters()
            hair_params.hair_type = unreal.HairType.STANDARD
            hair_params.strand_count = 50000
            hair_params.length_variation = 0.2
            hair_params.curl_intensity = 0.3
            hair_params.thickness = 0.02

            # Configurar cor do cabelo
            hair_color_params = unreal.HairColorData()
            hair_color_params.root_color = unreal.LinearColor(
                metahuman_config.hair_color[0],
                metahuman_config.hair_color[1],
                metahuman_config.hair_color[2],
                1.0
            )
            hair_color_params.tip_color = hair_color_params.root_color
            hair_color_params.color_variation = 0.1

            # Gerar groom asset
            groom_asset = self.metahuman_bridge_api.generate_procedural_hair(hair_params)

            if groom_asset:
                # Aplicar cor do cabelo
                hair_texture = self.metahuman_bridge_api.generate_hair_color_texture(
                    hair_color_params,
                    unreal.IntPoint(1024, 1024)
                )

                if hair_texture:
                    # Aplicar textura ao groom
                    self.metahuman_bridge_api.apply_hair_texture(groom_asset, hair_texture)

                logger.info("✓ Cabelo gerado")
                return groom_asset
            else:
                logger.error("Falha ao gerar cabelo")
                return None

        except Exception as e:
            logger.error(f"Erro ao gerar cabelo: {str(e)}")
            return None

    def _create_animation_blueprint(self, config: ChampionConfiguration):
        """Criar Animation Blueprint para o campeão"""
        try:
            if not self.metahuman_bridge_api:
                logger.error("MetaHuman Bridge API não disponível")
                return None

            # Parâmetros do Animation Blueprint
            anim_params = unreal.AnimationBlueprintGenerationParameters()
            anim_params.blueprint_name = f"ABP_{config.champion_id}"
            anim_params.target_skeleton = None  # Será definido após criação do mesh
            anim_params.include_facial_animation = True
            anim_params.include_body_animation = True
            anim_params.include_lip_sync = True
            anim_params.optimization_level = unreal.AnimationOptimizationLevel.HIGH

            # Configurar dados de animação facial
            facial_data = unreal.FacialAnimationData()
            facial_data.enable_eye_tracking = True
            facial_data.enable_blink_animation = True
            facial_data.enable_jaw_movement = True
            facial_data.emotion_intensity = 1.0
            anim_params.facial_animation_data = facial_data

            # Configurar lip sync
            lip_sync_data = unreal.LipSyncData()
            lip_sync_data.enable_phoneme_mapping = True
            lip_sync_data.phoneme_intensity = 1.0
            lip_sync_data.smoothing_factor = 0.5
            anim_params.lip_sync_data = lip_sync_data

            # Gerar Animation Blueprint
            anim_blueprint = self.metahuman_bridge_api.generate_animation_blueprint(anim_params)

            if anim_blueprint:
                logger.info("✓ Animation Blueprint criado")
                return anim_blueprint
            else:
                logger.error("Falha ao criar Animation Blueprint")
                return None

        except Exception as e:
            logger.error(f"Erro ao criar Animation Blueprint: {str(e)}")
            return None

    def _create_champion_bridge_config(self, config: ChampionConfiguration,
                                     metahuman_mesh, anim_blueprint, textures, hair_asset):
        """Criar configuração para o Champions Bridge"""
        try:
            # Criar configuração visual
            visual_config = unreal.AuracronChampionVisualConfig()
            visual_config.metahuman_mesh = metahuman_mesh
            visual_config.animation_blueprint = anim_blueprint
            visual_config.champion_scale = config.metahuman_config.scale

            # Configurar cores
            visual_config.primary_color = unreal.LinearColor(
                config.metahuman_config.skin_tone[0],
                config.metahuman_config.skin_tone[1],
                config.metahuman_config.skin_tone[2],
                1.0
            )

            # Configurar texturas
            if 'skin_diffuse' in textures:
                visual_config.alternative_skin_textures = [textures['skin_diffuse']]
            if 'skin_normal' in textures:
                visual_config.alternative_skin_normals = [textures['skin_normal']]

            # Configurar cabelo
            if hair_asset:
                visual_config.hair_groom_asset = hair_asset

            visual_config.use_metahuman = True

            # Criar atributos base
            base_attributes = unreal.AuracronChampionBaseAttributes()
            base_attributes.max_health = config.attributes.max_health
            base_attributes.current_health = config.attributes.current_health
            base_attributes.max_mana = config.attributes.max_mana
            base_attributes.current_mana = config.attributes.current_mana
            base_attributes.attack_damage = config.attributes.attack_damage
            base_attributes.ability_power = config.attributes.ability_power
            base_attributes.armor = config.attributes.armor
            base_attributes.magic_resistance = config.attributes.magic_resistance
            base_attributes.movement_speed = config.attributes.movement_speed
            base_attributes.attack_speed = config.attributes.attack_speed
            base_attributes.critical_chance = config.attributes.critical_chance
            base_attributes.critical_damage = config.attributes.critical_damage
            base_attributes.health_regeneration = config.attributes.health_regeneration
            base_attributes.mana_regeneration = config.attributes.mana_regeneration

            # Criar configuração de habilidades
            abilities = unreal.AuracronChampionAbilities()
            abilities.available_ability_points = 18

            # Configurar cooldowns e custos baseados no tipo do campeão
            self._configure_abilities_by_type(abilities, config.champion_type)

            # Criar configuração completa
            champion_config = unreal.AuracronChampionConfiguration()
            champion_config.champion_id = config.champion_id
            champion_config.champion_name = unreal.Text.from_string(config.champion_name)
            champion_config.champion_description = unreal.Text.from_string(config.description)

            # Mapear tipo do campeão
            type_map = {
                ChampionType.WARRIOR: unreal.AuracronChampionType.WARRIOR,
                ChampionType.MAGE: unreal.AuracronChampionType.MAGE,
                ChampionType.ASSASSIN: unreal.AuracronChampionType.ASSASSIN,
                ChampionType.SUPPORT: unreal.AuracronChampionType.SUPPORT,
                ChampionType.TANK: unreal.AuracronChampionType.TANK,
                ChampionType.MARKSMAN: unreal.AuracronChampionType.MARKSMAN
            }
            champion_config.champion_type = type_map.get(config.champion_type, unreal.AuracronChampionType.WARRIOR)

            # Mapear raridade
            rarity_map = {
                ChampionRarity.COMMON: unreal.AuracronChampionRarity.COMMON,
                ChampionRarity.UNCOMMON: unreal.AuracronChampionRarity.UNCOMMON,
                ChampionRarity.RARE: unreal.AuracronChampionRarity.RARE,
                ChampionRarity.EPIC: unreal.AuracronChampionRarity.EPIC,
                ChampionRarity.LEGENDARY: unreal.AuracronChampionRarity.LEGENDARY,
                ChampionRarity.MYTHIC: unreal.AuracronChampionRarity.MYTHIC
            }
            champion_config.champion_rarity = rarity_map.get(config.champion_rarity, unreal.AuracronChampionRarity.COMMON)

            champion_config.base_attributes = base_attributes
            champion_config.abilities = abilities
            champion_config.visual_config = visual_config

            logger.info("✓ Configuração do Champions Bridge criada")
            return champion_config

        except Exception as e:
            logger.error(f"Erro ao criar configuração do Champions Bridge: {str(e)}")
            return None

    def _configure_abilities_by_type(self, abilities, champion_type: ChampionType):
        """Configurar habilidades baseadas no tipo do campeão"""
        try:
            # Configurações base por tipo
            if champion_type == ChampionType.WARRIOR:
                abilities.ability_cooldowns = {
                    'Q': 8.0, 'W': 12.0, 'E': 15.0, 'R': 100.0
                }
                abilities.ability_mana_costs = {
                    'Q': 50.0, 'W': 70.0, 'E': 80.0, 'R': 100.0
                }
            elif champion_type == ChampionType.MAGE:
                abilities.ability_cooldowns = {
                    'Q': 6.0, 'W': 10.0, 'E': 14.0, 'R': 80.0
                }
                abilities.ability_mana_costs = {
                    'Q': 60.0, 'W': 90.0, 'E': 100.0, 'R': 150.0
                }
            elif champion_type == ChampionType.ASSASSIN:
                abilities.ability_cooldowns = {
                    'Q': 5.0, 'W': 8.0, 'E': 12.0, 'R': 60.0
                }
                abilities.ability_mana_costs = {
                    'Q': 40.0, 'W': 60.0, 'E': 70.0, 'R': 80.0
                }
            elif champion_type == ChampionType.SUPPORT:
                abilities.ability_cooldowns = {
                    'Q': 10.0, 'W': 15.0, 'E': 18.0, 'R': 120.0
                }
                abilities.ability_mana_costs = {
                    'Q': 70.0, 'W': 100.0, 'E': 120.0, 'R': 150.0
                }
            elif champion_type == ChampionType.TANK:
                abilities.ability_cooldowns = {
                    'Q': 12.0, 'W': 18.0, 'E': 20.0, 'R': 140.0
                }
                abilities.ability_mana_costs = {
                    'Q': 60.0, 'W': 80.0, 'E': 90.0, 'R': 120.0
                }
            elif champion_type == ChampionType.MARKSMAN:
                abilities.ability_cooldowns = {
                    'Q': 7.0, 'W': 11.0, 'E': 16.0, 'R': 90.0
                }
                abilities.ability_mana_costs = {
                    'Q': 45.0, 'W': 65.0, 'E': 75.0, 'R': 100.0
                }

            # Inicializar níveis das habilidades
            abilities.ability_levels = {
                'Q': 0, 'W': 0, 'E': 0, 'R': 0
            }

        except Exception as e:
            logger.error(f"Erro ao configurar habilidades: {str(e)}")

    def _register_champion(self, champion_id: str, champion_config) -> bool:
        """Registrar campeão no Champions Bridge"""
        try:
            if not self.champions_bridge:
                logger.error("Champions Bridge não disponível")
                return False

            # Registrar configuração no Champions Bridge
            self.champions_bridge.set_champion_configuration(champion_id, champion_config)

            # Verificar se foi registrado com sucesso
            available_champions = self.champions_bridge.get_available_champions()
            if champion_id in available_champions:
                logger.info(f"✓ Campeão {champion_id} registrado com sucesso")
                return True
            else:
                logger.error(f"Falha ao registrar campeão {champion_id}")
                return False

        except Exception as e:
            logger.error(f"Erro ao registrar campeão: {str(e)}")
            return False

    def _save_champion_assets(self, champion_id: str, assets: Dict[str, Any]):
        """Salvar assets do campeão"""
        try:
            base_path = f"/Game/Champions/{champion_id}/"

            # Salvar mesh
            if 'mesh' in assets and assets['mesh']:
                mesh_path = f"{base_path}Meshes/SK_{champion_id}"
                unreal.EditorAssetLibrary.save_asset(mesh_path, assets['mesh'])
                logger.info(f"✓ Mesh salvo em: {mesh_path}")

            # Salvar Animation Blueprint
            if 'animation_blueprint' in assets and assets['animation_blueprint']:
                anim_path = f"{base_path}Animations/ABP_{champion_id}"
                unreal.EditorAssetLibrary.save_asset(anim_path, assets['animation_blueprint'])
                logger.info(f"✓ Animation Blueprint salvo em: {anim_path}")

            # Salvar texturas
            if 'textures' in assets and assets['textures']:
                for texture_name, texture in assets['textures'].items():
                    if texture:
                        texture_path = f"{base_path}Textures/T_{champion_id}_{texture_name}"
                        unreal.EditorAssetLibrary.save_asset(texture_path, texture)
                        logger.info(f"✓ Textura {texture_name} salva em: {texture_path}")

            # Salvar cabelo
            if 'hair' in assets and assets['hair']:
                hair_path = f"{base_path}Hair/GR_{champion_id}_Hair"
                unreal.EditorAssetLibrary.save_asset(hair_path, assets['hair'])
                logger.info(f"✓ Cabelo salvo em: {hair_path}")

        except Exception as e:
            logger.error(f"Erro ao salvar assets: {str(e)}")

    def _get_default_textures(self) -> Dict[str, Any]:
        """Obter texturas padrão"""
        try:
            default_textures = {}

            # Tentar carregar texturas padrão do MetaHuman
            skin_texture = unreal.EditorAssetLibrary.load_asset('/Game/MetaHumans/Common/Textures/T_DefaultSkin')
            if skin_texture:
                default_textures['skin_diffuse'] = skin_texture

            eye_texture = unreal.EditorAssetLibrary.load_asset('/Game/MetaHumans/Common/Textures/T_DefaultEyes')
            if eye_texture:
                default_textures['eye_diffuse'] = eye_texture

            return default_textures

        except Exception as e:
            logger.error(f"Erro ao obter texturas padrão: {str(e)}")
            return {}

    def _get_default_hair(self):
        """Obter cabelo padrão"""
        try:
            # Tentar carregar groom padrão
            default_hair = unreal.EditorAssetLibrary.load_asset('/Game/MetaHumans/Common/Hair/GR_DefaultHair')
            return default_hair

        except Exception as e:
            logger.error(f"Erro ao obter cabelo padrão: {str(e)}")
            return None

    def spawn_champion(self, champion_id: str, location: Tuple[float, float, float] = (0, 0, 0)) -> bool:
        """Spawnar campeão no mundo"""
        try:
            if not self.champions_bridge:
                logger.error("Champions Bridge não disponível")
                return False

            # Selecionar campeão
            success = self.champions_bridge.select_champion(champion_id)
            if not success:
                logger.error(f"Falha ao selecionar campeão: {champion_id}")
                return False

            # Spawnar no mundo
            spawn_location = unreal.Vector(location[0], location[1], location[2])
            spawn_rotation = unreal.Rotator(0, 0, 0)

            success = self.champions_bridge.spawn_champion(spawn_location, spawn_rotation)
            if success:
                logger.info(f"✓ Campeão {champion_id} spawnado em {location}")
                return True
            else:
                logger.error(f"Falha ao spawnar campeão: {champion_id}")
                return False

        except Exception as e:
            logger.error(f"Erro ao spawnar campeão: {str(e)}")
            return False

    def get_champion_info(self, champion_id: str) -> Optional[Dict[str, Any]]:
        """Obter informações de um campeão"""
        try:
            if not self.champions_bridge:
                logger.error("Champions Bridge não disponível")
                return None

            # Obter configuração do campeão
            config = self.champions_bridge.get_champion_configuration(champion_id)
            if not config:
                logger.error(f"Campeão não encontrado: {champion_id}")
                return None

            # Converter para dicionário Python
            info = {
                'champion_id': config.champion_id,
                'champion_name': str(config.champion_name),
                'champion_type': str(config.champion_type),
                'champion_rarity': str(config.champion_rarity),
                'description': str(config.champion_description),
                'attributes': {
                    'max_health': config.base_attributes.max_health,
                    'max_mana': config.base_attributes.max_mana,
                    'attack_damage': config.base_attributes.attack_damage,
                    'ability_power': config.base_attributes.ability_power,
                    'armor': config.base_attributes.armor,
                    'magic_resistance': config.base_attributes.magic_resistance,
                    'movement_speed': config.base_attributes.movement_speed,
                    'attack_speed': config.base_attributes.attack_speed
                },
                'visual_config': {
                    'use_metahuman': config.visual_config.use_metahuman,
                    'champion_scale': config.visual_config.champion_scale
                }
            }

            return info

        except Exception as e:
            logger.error(f"Erro ao obter informações do campeão: {str(e)}")
            return None

    def list_champions(self) -> List[str]:
        """Listar todos os campeões disponíveis"""
        try:
            if not self.champions_bridge:
                logger.error("Champions Bridge não disponível")
                return []

            champions = self.champions_bridge.get_available_champions()
            logger.info(f"Campeões disponíveis: {len(champions)}")
            return champions

        except Exception as e:
            logger.error(f"Erro ao listar campeões: {str(e)}")
            return []


# ============================================================================
# FUNÇÕES DE EXEMPLO E TEMPLATES
# ============================================================================

def create_example_warrior_champion() -> ChampionConfiguration:
    """Criar configuração de exemplo para um guerreiro"""

    # Configuração MetaHuman
    metahuman_config = MetaHumanConfig(
        character_name="Thorgar the Mighty",
        gender=MetaHumanGender.MALE,
        archetype=MetaHumanArchetype.ATHLETIC,
        age=35,
        scale=1.1,
        skin_tone=(0.9, 0.8, 0.7),  # Tom de pele bronzeado
        hair_color=(0.3, 0.2, 0.1),  # Cabelo castanho escuro
        eye_color=(0.2, 0.4, 0.8)   # Olhos azuis
    )

    # Atributos de guerreiro
    attributes = ChampionAttributes(
        max_health=1500.0,
        current_health=1500.0,
        max_mana=400.0,
        current_mana=400.0,
        attack_damage=150.0,
        ability_power=60.0,
        armor=50.0,
        magic_resistance=35.0,
        movement_speed=340.0,
        attack_speed=1.2,
        critical_chance=0.08,
        critical_damage=2.2,
        health_regeneration=8.0,
        mana_regeneration=6.0
    )

    # Configuração completa
    config = ChampionConfiguration(
        champion_id="thorgar_warrior",
        champion_name="Thorgar, o Poderoso",
        champion_type=ChampionType.WARRIOR,
        champion_rarity=ChampionRarity.EPIC,
        attributes=attributes,
        metahuman_config=metahuman_config,
        description="Um guerreiro lendário das montanhas do norte, conhecido por sua força incomparável.",
        lore="Thorgar nasceu nas geladas montanhas do norte, onde aprendeu a lutar contra as criaturas mais ferozes..."
    )

    return config

def create_example_mage_champion() -> ChampionConfiguration:
    """Criar configuração de exemplo para um mago"""

    # Configuração MetaHuman
    metahuman_config = MetaHumanConfig(
        character_name="Lyralei the Arcane",
        gender=MetaHumanGender.FEMALE,
        archetype=MetaHumanArchetype.SLIM,
        age=28,
        scale=0.95,
        skin_tone=(0.95, 0.9, 0.85),  # Tom de pele claro
        hair_color=(0.8, 0.7, 0.9),  # Cabelo prateado/roxo
        eye_color=(0.7, 0.3, 0.9)    # Olhos violeta
    )

    # Atributos de mago
    attributes = ChampionAttributes(
        max_health=800.0,
        current_health=800.0,
        max_mana=1000.0,
        current_mana=1000.0,
        attack_damage=60.0,
        ability_power=200.0,
        armor=20.0,
        magic_resistance=40.0,
        movement_speed=330.0,
        attack_speed=0.8,
        critical_chance=0.03,
        critical_damage=1.8,
        health_regeneration=4.0,
        mana_regeneration=15.0
    )

    # Configuração completa
    config = ChampionConfiguration(
        champion_id="lyralei_mage",
        champion_name="Lyralei, a Arcana",
        champion_type=ChampionType.MAGE,
        champion_rarity=ChampionRarity.LEGENDARY,
        attributes=attributes,
        metahuman_config=metahuman_config,
        description="Uma maga poderosa que domina as artes arcanas e os segredos do cosmos.",
        lore="Lyralei estudou nas torres mais altas da academia arcana, onde descobriu segredos perdidos..."
    )

    return config

def create_example_assassin_champion() -> ChampionConfiguration:
    """Criar configuração de exemplo para um assassino"""

    # Configuração MetaHuman
    metahuman_config = MetaHumanConfig(
        character_name="Kael Shadowblade",
        gender=MetaHumanGender.MALE,
        archetype=MetaHumanArchetype.SLIM,
        age=26,
        scale=1.0,
        skin_tone=(0.7, 0.65, 0.6),  # Tom de pele moreno
        hair_color=(0.1, 0.1, 0.1),  # Cabelo preto
        eye_color=(0.9, 0.1, 0.1)    # Olhos vermelhos
    )

    # Atributos de assassino
    attributes = ChampionAttributes(
        max_health=1000.0,
        current_health=1000.0,
        max_mana=600.0,
        current_mana=600.0,
        attack_damage=180.0,
        ability_power=100.0,
        armor=25.0,
        magic_resistance=30.0,
        movement_speed=380.0,
        attack_speed=1.5,
        critical_chance=0.15,
        critical_damage=2.5,
        health_regeneration=6.0,
        mana_regeneration=10.0
    )

    # Configuração completa
    config = ChampionConfiguration(
        champion_id="kael_assassin",
        champion_name="Kael Lâmina Sombria",
        champion_type=ChampionType.ASSASSIN,
        champion_rarity=ChampionRarity.RARE,
        attributes=attributes,
        metahuman_config=metahuman_config,
        description="Um assassino das sombras, mestre em eliminar alvos silenciosamente.",
        lore="Kael cresceu nas ruas sombrias da cidade baixa, onde aprendeu a arte do assassinato..."
    )

    return config


# ============================================================================
# SCRIPT PRINCIPAL E FUNÇÕES DE UTILIDADE
# ============================================================================

def main():
    """Função principal do script"""
    try:
        logger.info("=== AURACRON Champion Creator ===")
        logger.info("Iniciando criação de campeões MetaHuman...")

        # Inicializar criador
        creator = AuracronChampionCreator()

        if not creator.initialized:
            logger.error("Falha na inicialização do sistema")
            return False

        # Criar campeões de exemplo
        logger.info("\n--- Criando Campeões de Exemplo ---")

        # Guerreiro
        warrior_config = create_example_warrior_champion()
        success = creator.create_champion(warrior_config)
        if success:
            logger.info(f"✓ Guerreiro criado: {warrior_config.champion_name}")

        # Mago
        mage_config = create_example_mage_champion()
        success = creator.create_champion(mage_config)
        if success:
            logger.info(f"✓ Mago criado: {mage_config.champion_name}")

        # Assassino
        assassin_config = create_example_assassin_champion()
        success = creator.create_champion(assassin_config)
        if success:
            logger.info(f"✓ Assassino criado: {assassin_config.champion_name}")

        # Listar campeões criados
        logger.info("\n--- Campeões Disponíveis ---")
        champions = creator.list_champions()
        for champion_id in champions:
            info = creator.get_champion_info(champion_id)
            if info:
                logger.info(f"• {info['champion_name']} ({info['champion_type']}) - {info['champion_rarity']}")

        logger.info("\n✓ Script executado com sucesso!")
        return True

    except Exception as e:
        logger.error(f"Erro na execução principal: {str(e)}")
        return False

def create_custom_champion(champion_data: Dict[str, Any]) -> bool:
    """
    Criar campeão personalizado a partir de dados JSON

    Args:
        champion_data: Dicionário com dados do campeão

    Returns:
        bool: True se criado com sucesso
    """
    try:
        creator = AuracronChampionCreator()

        if not creator.initialized:
            logger.error("Sistema não inicializado")
            return False

        # Converter dados para configuração
        config = _convert_data_to_config(champion_data)
        if not config:
            logger.error("Falha ao converter dados")
            return False

        # Criar campeão
        return creator.create_champion(config)

    except Exception as e:
        logger.error(f"Erro ao criar campeão personalizado: {str(e)}")
        return False

def _convert_data_to_config(data: Dict[str, Any]) -> Optional[ChampionConfiguration]:
    """Converter dados JSON para ChampionConfiguration"""
    try:
        # Implementar conversão de dados JSON para objetos Python
        # Esta função seria expandida baseada no formato específico dos dados
        pass

    except Exception as e:
        logger.error(f"Erro na conversão de dados: {str(e)}")
        return None


# ============================================================================
# EXECUÇÃO DO SCRIPT
# ============================================================================

if __name__ == "__main__":
    # Executar script principal
    main()
else:
    # Quando importado como módulo
    logger.info("AURACRON Champion Creator carregado como módulo")
    logger.info("Use main() para executar exemplos ou create_custom_champion() para criar campeões personalizados")
