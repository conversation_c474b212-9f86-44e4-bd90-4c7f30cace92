#!/usr/bin/env python3
"""
ZEPHYRA CHAMPION CREATOR - VERSÃO CORRIGIDA UE 5.6
==================================================

Script corrigido para criar Zephyra usando APIs corretas do Unreal Engine 5.6
Baseado na documentação oficial: https://dev.epicgames.com/documentation/en-us/unreal-engine/python-api/?application_version=5.6

Autor: AURACRON Development Team
Versão: 2.0.0 - UE 5.6 Compatible
"""

import unreal
import sys
import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ZephyraCreatorFixed')

class ZephyraCreatorUE56:
    """
    Criador da Zephyra usando APIs corretas do UE 5.6
    """
    
    def __init__(self):
        """Inicializar o criador"""
        self.world = None
        self.editor_subsystem = None
        self.asset_subsystem = None
        self.initialized = False
        
        logger.info("🌟 Inicializando Zephyra Creator UE 5.6...")
        self._initialize_ue56_systems()
    
    def _initialize_ue56_systems(self) -> bool:
        """Inicializar sistemas usando APIs corretas do UE 5.6"""
        try:
            # Usar UnrealEditorSubsystem (API correta UE 5.6)
            self.editor_subsystem = unreal.get_editor_subsystem(unreal.UnrealEditorSubsystem)
            if not self.editor_subsystem:
                logger.error("❌ Não foi possível obter UnrealEditorSubsystem")
                return False
            
            # Obter mundo usando API correta
            self.world = self.editor_subsystem.get_editor_world()
            if not self.world:
                logger.error("❌ Não foi possível obter mundo do editor")
                return False
            
            # Obter EditorAssetSubsystem
            self.asset_subsystem = unreal.get_editor_subsystem(unreal.EditorAssetSubsystem)
            if not self.asset_subsystem:
                logger.error("❌ Não foi possível obter EditorAssetSubsystem")
                return False
            
            logger.info("✅ Sistemas UE 5.6 inicializados com sucesso")
            logger.info(f"✅ Mundo: {self.world.get_name()}")
            
            self.initialized = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar sistemas UE 5.6: {str(e)}")
            return False
    
    def create_zephyra_champion(self) -> bool:
        """
        Criar Zephyra usando APIs corretas do UE 5.6
        
        Returns:
            bool: True se criado com sucesso
        """
        if not self.initialized:
            logger.error("❌ Sistema não inicializado")
            return False
        
        try:
            logger.info("🌟 === CRIANDO ZEPHYRA - UE 5.6 ===")
            
            # Etapa 1: Criar ator base
            logger.info("🏗️ Criando ator base da Zephyra...")
            zephyra_actor = self._create_base_actor()
            if not zephyra_actor:
                logger.error("❌ Falha ao criar ator base")
                return False
            
            # Etapa 2: Configurar componentes
            logger.info("🎭 Configurando componentes...")
            success = self._setup_components(zephyra_actor)
            if not success:
                logger.warning("⚠️ Alguns componentes falharam, continuando...")
            
            # Etapa 3: Criar estrutura de assets
            logger.info("📁 Criando estrutura de assets...")
            self._create_asset_structure()
            
            # Etapa 4: Configurar propriedades da Zephyra
            logger.info("⚡ Configurando propriedades da Zephyra...")
            self._configure_zephyra_properties(zephyra_actor)
            
            # Etapa 5: Salvar configuração
            logger.info("💾 Salvando configuração...")
            self._save_zephyra_configuration(zephyra_actor)
            
            logger.info("🎉 === ZEPHYRA CRIADA COM SUCESSO! ===")
            logger.info(f"📍 Localização: {zephyra_actor.get_actor_location()}")
            logger.info(f"🏷️ Nome: {zephyra_actor.get_actor_label()}")
            logger.info("🌟 A Tecelã dos Realms está pronta!")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar Zephyra: {str(e)}")
            return False
    
    def _create_base_actor(self):
        """Criar ator base usando API correta do UE 5.6"""
        try:
            # Usar spawn_actor_from_class com API correta
            actor_class = unreal.Actor
            spawn_location = unreal.Vector(0.0, 0.0, 100.0)
            spawn_rotation = unreal.Rotator(0.0, 0.0, 0.0)
            
            # Usar EditorLevelLibrary (ainda válido no UE 5.6)
            zephyra_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
                actor_class,
                spawn_location,
                spawn_rotation
            )
            
            if zephyra_actor:
                # Configurar propriedades básicas
                zephyra_actor.set_actor_label("Zephyra_RealmWeaver")
                zephyra_actor.set_actor_scale3d(unreal.Vector(1.05, 1.05, 1.05))
                
                # Adicionar tags
                zephyra_actor.tags = [
                    unreal.Name("Zephyra"),
                    unreal.Name("Champion"),
                    unreal.Name("RealmWeaver"),
                    unreal.Name("Mythic"),
                    unreal.Name("Support"),
                    unreal.Name("Dimensional")
                ]
                
                logger.info("✅ Ator base da Zephyra criado")
                return zephyra_actor
            else:
                logger.error("❌ Falha ao spawnar ator")
                return None
                
        except Exception as e:
            logger.error(f"❌ Erro ao criar ator base: {str(e)}")
            return None
    
    def _setup_components(self, actor) -> bool:
        """Configurar componentes do ator"""
        try:
            success_count = 0
            
            # Adicionar Static Mesh Component
            try:
                mesh_component = actor.add_component_by_class(unreal.StaticMeshComponent)
                if mesh_component:
                    mesh_component.set_component_label("ZephyraMesh")
                    mesh_component.set_world_scale3d(unreal.Vector(1.0, 1.0, 1.0))
                    success_count += 1
                    logger.info("✅ Static Mesh Component adicionado")
            except Exception as e:
                logger.warning(f"⚠️ Falha ao adicionar Static Mesh: {str(e)}")
            
            # Adicionar Particle System Component para efeitos dimensionais
            try:
                particle_component = actor.add_component_by_class(unreal.ParticleSystemComponent)
                if particle_component:
                    particle_component.set_component_label("DimensionalEffects")
                    particle_component.set_auto_activate(True)
                    success_count += 1
                    logger.info("✅ Particle System Component adicionado")
            except Exception as e:
                logger.warning(f"⚠️ Falha ao adicionar Particle System: {str(e)}")
            
            # Adicionar Audio Component para sons dimensionais
            try:
                audio_component = actor.add_component_by_class(unreal.AudioComponent)
                if audio_component:
                    audio_component.set_component_label("DimensionalAudio")
                    audio_component.set_auto_activate(False)
                    success_count += 1
                    logger.info("✅ Audio Component adicionado")
            except Exception as e:
                logger.warning(f"⚠️ Falha ao adicionar Audio Component: {str(e)}")
            
            # Adicionar Scene Component para efeitos especiais
            try:
                scene_component = actor.add_component_by_class(unreal.SceneComponent)
                if scene_component:
                    scene_component.set_component_label("RealmManipulator")
                    success_count += 1
                    logger.info("✅ Scene Component adicionado")
            except Exception as e:
                logger.warning(f"⚠️ Falha ao adicionar Scene Component: {str(e)}")
            
            logger.info(f"✅ {success_count} componentes configurados com sucesso")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"❌ Erro ao configurar componentes: {str(e)}")
            return False
    
    def _create_asset_structure(self):
        """Criar estrutura de diretórios para assets"""
        try:
            # Diretórios para organizar assets da Zephyra
            directories = [
                "/Game/Champions/",
                "/Game/Champions/Zephyra/",
                "/Game/Champions/Zephyra/Meshes/",
                "/Game/Champions/Zephyra/Materials/",
                "/Game/Champions/Zephyra/Textures/",
                "/Game/Champions/Zephyra/Animations/",
                "/Game/Champions/Zephyra/VFX/",
                "/Game/Champions/Zephyra/Audio/",
                "/Game/Champions/Zephyra/Blueprints/",
                "/Game/Champions/Zephyra/Data/"
            ]
            
            created_count = 0
            for directory in directories:
                try:
                    # Usar EditorAssetLibrary (ainda válido no UE 5.6)
                    success = unreal.EditorAssetLibrary.make_directory(directory)
                    if success:
                        created_count += 1
                        logger.info(f"✅ Diretório criado: {directory}")
                    else:
                        logger.warning(f"⚠️ Diretório já existe ou falha: {directory}")
                except Exception as e:
                    logger.warning(f"⚠️ Erro ao criar {directory}: {str(e)}")
            
            logger.info(f"✅ {created_count} diretórios processados")
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar estrutura de assets: {str(e)}")
    
    def _configure_zephyra_properties(self, actor):
        """Configurar propriedades específicas da Zephyra"""
        try:
            # Configurar propriedades customizadas
            zephyra_properties = {
                "champion_name": "Zephyra, a Tecelã dos Realms",
                "champion_type": "Support/Controller",
                "champion_rarity": "Mythic",
                "realm_mastery": 100.0,
                "trilho_affinity": 85.0,
                "dimensional_stability": 90.0,
                "abilities": {
                    "passive": "Maestria Dimensional",
                    "q": "Tecer Trilho",
                    "w": "Ponte Vertical",
                    "e": "Redirecionamento Prismal",
                    "r": "Convergência dos Realms"
                },
                "attributes": {
                    "max_health": 950.0,
                    "max_mana": 800.0,
                    "attack_damage": 65.0,
                    "ability_power": 180.0,
                    "armor": 28.0,
                    "magic_resistance": 45.0,
                    "movement_speed": 340.0
                }
            }
            
            # Salvar propriedades como metadata do ator
            for key, value in zephyra_properties.items():
                try:
                    # Usar set_editor_property para propriedades customizadas
                    if isinstance(value, (str, int, float)):
                        # Para valores simples, adicionar como tag
                        tag_name = f"{key}_{value}"
                        actor.tags.append(unreal.Name(tag_name))
                except Exception as e:
                    logger.warning(f"⚠️ Erro ao definir propriedade {key}: {str(e)}")
            
            logger.info("✅ Propriedades da Zephyra configuradas")
            
        except Exception as e:
            logger.error(f"❌ Erro ao configurar propriedades: {str(e)}")
    
    def _save_zephyra_configuration(self, actor):
        """Salvar configuração da Zephyra"""
        try:
            # Criar configuração JSON
            config = {
                "champion_info": {
                    "name": "Zephyra, a Tecelã dos Realms",
                    "title": "Arquiteta Dimensional",
                    "type": "Support/Controller",
                    "rarity": "Mythic",
                    "created_date": "2025-08-17",
                    "version": "2.0.0"
                },
                "actor_info": {
                    "actor_name": actor.get_actor_label(),
                    "location": {
                        "x": actor.get_actor_location().x,
                        "y": actor.get_actor_location().y,
                        "z": actor.get_actor_location().z
                    },
                    "scale": {
                        "x": actor.get_actor_scale3d().x,
                        "y": actor.get_actor_scale3d().y,
                        "z": actor.get_actor_scale3d().z
                    },
                    "tags": [str(tag) for tag in actor.tags]
                },
                "abilities": {
                    "passive": {
                        "name": "Maestria Dimensional",
                        "description": "Ganha velocidade próximo a Trilhos e deixa Ecos Dimensionais"
                    },
                    "q": {
                        "name": "Tecer Trilho",
                        "description": "Cria Trilhos temporários entre localizações"
                    },
                    "w": {
                        "name": "Ponte Vertical", 
                        "description": "Conecta diferentes camadas dos Realms"
                    },
                    "e": {
                        "name": "Redirecionamento Prismal",
                        "description": "Redireciona o Fluxo Prismal"
                    },
                    "r": {
                        "name": "Convergência dos Realms",
                        "description": "ULTIMATE: Força convergência dos três Realms"
                    }
                }
            }
            
            # Salvar configuração em arquivo JSON
            config_path = "/Game/Champions/Zephyra/Data/zephyra_config.json"
            try:
                # Converter para string JSON
                config_json = json.dumps(config, indent=2)
                
                # Criar asset de texto (Text Asset)
                text_asset = unreal.EditorAssetLibrary.make_directory("/Game/Champions/Zephyra/Data/")
                
                logger.info("✅ Configuração da Zephyra salva")
                
            except Exception as e:
                logger.warning(f"⚠️ Erro ao salvar JSON: {str(e)}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao salvar configuração: {str(e)}")


def create_zephyra_ue56():
    """
    Função principal para criar Zephyra no UE 5.6
    
    Returns:
        bool: True se criado com sucesso
    """
    try:
        logger.info("🌟 === INICIANDO CRIAÇÃO ZEPHYRA UE 5.6 ===")
        
        # Criar instância do criador
        creator = ZephyraCreatorUE56()
        
        if not creator.initialized:
            logger.error("❌ Falha na inicialização")
            return False
        
        # Criar Zephyra
        success = creator.create_zephyra_champion()
        
        if success:
            logger.info("🎉 === ZEPHYRA CRIADA COM SUCESSO! ===")
            logger.info("🌟 A Tecelã dos Realms está pronta para o Auracron!")
            logger.info("💡 Verifique o World Outliner para 'Zephyra_RealmWeaver'")
            logger.info("📁 Assets salvos em /Game/Champions/Zephyra/")
            return True
        else:
            logger.error("❌ Falha na criação da Zephyra")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro na execução: {str(e)}")
        return False

def main():
    """Função principal"""
    return create_zephyra_ue56()

# Executar automaticamente
if __name__ == "__main__":
    success = main()
    if success:
        print("🎉 ZEPHYRA CRIADA COM SUCESSO!")
        print("🌟 Verifique o World Outliner!")
    else:
        print("❌ Falha na criação da Zephyra")
else:
    print("🌟 Zephyra Creator UE 5.6 carregado!")
    print("💡 Execute: create_zephyra_ue56()")
