#!/usr/bin/env python3
"""
Teste de Criação da Zephyra - Script Simplificado
=================================================

Script de teste para verificar se o sistema de criação da Zephyra
está funcionando corretamente.
"""

import unreal
import sys
import os

def test_zephyra_creation():
    """Teste simplificado de criação da Zephyra"""
    try:
        print("🌟 === TESTE DE CRIAÇÃO DA ZEPHYRA ===")
        
        # Verificar se estamos no Unreal Engine
        try:
            world = unreal.EditorLevelLibrary.get_editor_world()
            if world:
                print("✅ Unreal Engine detectado")
                print(f"✅ Mundo atual: {world.get_name()}")
            else:
                print("❌ Mundo não encontrado")
                return False
        except Exception as e:
            print(f"❌ Erro ao acessar Unreal Engine: {str(e)}")
            return False
        
        # Verificar sistemas disponíveis
        print("\n🔍 Verificando sistemas disponíveis...")
        
        # Verificar atores no mundo
        all_actors = unreal.EditorLevelLibrary.get_all_level_actors()
        print(f"✅ Atores no mundo: {len(all_actors)}")
        
        # Procurar por sistemas Auracron
        auracron_actors = []
        for actor in all_actors:
            if actor and "Auracron" in actor.get_name():
                auracron_actors.append(actor.get_name())
        
        if auracron_actors:
            print(f"✅ Sistemas Auracron encontrados: {len(auracron_actors)}")
            for actor_name in auracron_actors[:5]:  # Mostrar apenas os primeiros 5
                print(f"   • {actor_name}")
        else:
            print("⚠️ Nenhum sistema Auracron encontrado")
        
        # Verificar se podemos criar atores
        print("\n🏗️ Testando criação de ator...")
        try:
            actor_class = unreal.load_class(None, '/Script/Engine.Actor')
            if actor_class:
                test_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
                    actor_class, 
                    unreal.Vector(0, 0, 0)
                )
                if test_actor:
                    test_actor.set_actor_label("ZephyraTestActor")
                    print("✅ Ator de teste criado com sucesso")
                    
                    # Limpar ator de teste
                    unreal.EditorLevelLibrary.destroy_actor(test_actor)
                    print("✅ Ator de teste removido")
                else:
                    print("❌ Falha ao criar ator de teste")
            else:
                print("❌ Classe de ator não encontrada")
        except Exception as e:
            print(f"❌ Erro ao testar criação de ator: {str(e)}")
        
        # Verificar assets disponíveis
        print("\n📦 Verificando assets disponíveis...")
        try:
            # Verificar se existem assets MetaHuman
            metahuman_assets = unreal.EditorAssetLibrary.list_assets("/Game/MetaHumans/")
            if metahuman_assets:
                print(f"✅ Assets MetaHuman encontrados: {len(metahuman_assets)}")
            else:
                print("⚠️ Nenhum asset MetaHuman encontrado")
            
            # Verificar assets Auracron
            auracron_assets = unreal.EditorAssetLibrary.list_assets("/Game/Auracron/")
            if auracron_assets:
                print(f"✅ Assets Auracron encontrados: {len(auracron_assets)}")
            else:
                print("⚠️ Nenhum asset Auracron encontrado")
                
        except Exception as e:
            print(f"❌ Erro ao verificar assets: {str(e)}")
        
        # Teste de criação de diretório
        print("\n📁 Testando criação de diretórios...")
        try:
            test_directory = "/Game/Champions/Zephyra/Test/"
            success = unreal.EditorAssetLibrary.make_directory(test_directory)
            if success:
                print("✅ Diretório de teste criado")
                # Remover diretório de teste
                unreal.EditorAssetLibrary.delete_directory(test_directory)
                print("✅ Diretório de teste removido")
            else:
                print("❌ Falha ao criar diretório de teste")
        except Exception as e:
            print(f"❌ Erro ao testar diretórios: {str(e)}")
        
        print("\n🎉 === TESTE CONCLUÍDO ===")
        print("✅ Sistema básico funcionando")
        print("🌟 Pronto para criar Zephyra!")
        return True
        
    except Exception as e:
        print(f"❌ Erro geral no teste: {str(e)}")
        return False

def main():
    """Função principal"""
    return test_zephyra_creation()

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 TESTE BEM-SUCEDIDO!")
        print("🌟 Sistema pronto para criar Zephyra!")
    else:
        print("\n❌ TESTE FALHOU!")
        print("🔧 Verifique a configuração do sistema")
else:
    print("🧪 Módulo de teste da Zephyra carregado")
