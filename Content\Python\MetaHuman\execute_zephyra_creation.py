#!/usr/bin/env python3
"""
Executor Simples da Zephyra
===========================

Script simplificado para executar a criação da Zephyra
diretamente no Unreal Engine.
"""

import unreal
import sys
import os

def execute_zephyra_creation():
    """Executar criação da Zephyra de forma simplificada"""
    try:
        print("🌟 === EXECUTANDO CRIAÇÃO DA ZEPHYRA ===")
        
        # Importar o módulo de criação
        sys.path.append(os.path.join(os.getcwd(), 'Content', 'Python', 'MetaHuman'))
        
        try:
            from create_zephyra_champion import create_zephyra_revolutionary_champion
            print("✅ Módulo da Zephyra importado com sucesso")
        except ImportError as e:
            print(f"❌ Erro ao importar módulo: {str(e)}")
            return False
        
        # Executar criação
        print("🚀 Iniciando criação da Zephyra...")
        success = create_zephyra_revolutionary_champion()
        
        if success:
            print("🎉 === ZEPHYRA CRIADA COM SUCESSO! ===")
            print("🌟 A Tecelã dos Realms está pronta!")
            return True
        else:
            print("❌ Falha na criação da Zephyra")
            return False
            
    except Exception as e:
        print(f"❌ Erro na execução: {str(e)}")
        return False

# Executar automaticamente quando o script for carregado
if __name__ == "__main__":
    execute_zephyra_creation()
else:
    # Quando importado, criar função global
    unreal.log("🌟 Executor da Zephyra carregado!")
    unreal.log("💡 Use: execute_zephyra_creation() para criar a Zephyra")
