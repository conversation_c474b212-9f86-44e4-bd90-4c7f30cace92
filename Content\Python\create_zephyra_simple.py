#!/usr/bin/env python3
"""
CRIADOR SIMPLES DA ZEPHYRA
==========================

Script simplificado para criar Zephyra no Unreal Engine.
Execute este arquivo diretamente no console Python do UE.
"""

import unreal

def create_zephyra_simple():
    """Criar Zephyra de forma simplificada"""
    try:
        print("🌟 === CRIANDO ZEPHYRA - VERSÃO SIMPLIFICADA ===")
        
        # Verificar se estamos no Unreal Engine
        world = unreal.EditorLevelLibrary.get_editor_world()
        if not world:
            print("❌ Unreal Engine não detectado")
            return False
        
        print("✅ Unreal Engine detectado")
        print(f"✅ Mundo: {world.get_name()}")
        
        # Criar ator base para Zephyra
        print("🏗️ Criando ator base da Zephyra...")
        actor_class = unreal.load_class(None, '/Script/Engine.Actor')
        
        if not actor_class:
            print("❌ Classe de ator não encontrada")
            return False
        
        # Spawnar ator da Zephyra
        zephyra_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            actor_class,
            unreal.Vector(0, 0, 100)  # Spawnar um pouco acima do chão
        )
        
        if not zephyra_actor:
            print("❌ Falha ao criar ator da Zephyra")
            return False
        
        # Configurar ator
        zephyra_actor.set_actor_label("Zephyra_RealmWeaver")
        zephyra_actor.set_actor_scale3d(unreal.Vector(1.05, 1.05, 1.05))  # Ligeiramente maior
        
        print("✅ Ator base da Zephyra criado")
        
        # Adicionar componente de mesh
        print("🎭 Adicionando componente de mesh...")
        try:
            mesh_component = zephyra_actor.add_component_by_class(unreal.StaticMeshComponent)
            if mesh_component:
                mesh_component.set_component_label("ZephyraMesh")
                print("✅ Componente de mesh adicionado")
            else:
                print("⚠️ Falha ao adicionar componente de mesh")
        except Exception as e:
            print(f"⚠️ Erro ao adicionar mesh: {str(e)}")
        
        # Adicionar componente de partículas para efeitos dimensionais
        print("✨ Adicionando efeitos dimensionais...")
        try:
            particle_component = zephyra_actor.add_component_by_class(unreal.ParticleSystemComponent)
            if particle_component:
                particle_component.set_component_label("DimensionalEffects")
                print("✅ Efeitos dimensionais adicionados")
            else:
                print("⚠️ Falha ao adicionar efeitos")
        except Exception as e:
            print(f"⚠️ Erro ao adicionar efeitos: {str(e)}")
        
        # Criar diretório para assets da Zephyra
        print("📁 Criando diretórios...")
        directories = [
            "/Game/Champions/",
            "/Game/Champions/Zephyra/",
            "/Game/Champions/Zephyra/Meshes/",
            "/Game/Champions/Zephyra/Textures/",
            "/Game/Champions/Zephyra/Animations/",
            "/Game/Champions/Zephyra/VFX/",
            "/Game/Champions/Zephyra/Audio/"
        ]
        
        for directory in directories:
            try:
                unreal.EditorAssetLibrary.make_directory(directory)
                print(f"✅ Diretório criado: {directory}")
            except Exception as e:
                print(f"⚠️ Erro ao criar {directory}: {str(e)}")
        
        # Salvar informações da Zephyra
        print("💾 Salvando configuração...")
        zephyra_info = {
            "name": "Zephyra, a Tecelã dos Realms",
            "type": "Support/Controller",
            "rarity": "Mythic",
            "actor_name": zephyra_actor.get_name(),
            "location": str(zephyra_actor.get_actor_location()),
            "abilities": {
                "passive": "Maestria Dimensional",
                "q": "Tecer Trilho",
                "w": "Ponte Vertical", 
                "e": "Redirecionamento Prismal",
                "r": "Convergência dos Realms"
            }
        }
        
        # Adicionar tags ao ator
        zephyra_actor.tags = [
            "Zephyra",
            "Champion", 
            "RealmWeaver",
            "Mythic",
            "Support",
            "Dimensional"
        ]
        
        print("🎉 === ZEPHYRA CRIADA COM SUCESSO! ===")
        print(f"📍 Localização: {zephyra_actor.get_actor_location()}")
        print(f"🏷️ Nome do Ator: {zephyra_actor.get_name()}")
        print("🌟 A Tecelã dos Realms está pronta!")
        print("💡 Você pode encontrá-la no World Outliner")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro geral: {str(e)}")
        return False

def main():
    """Função principal"""
    return create_zephyra_simple()

# Executar automaticamente
if __name__ == "__main__":
    main()
else:
    print("🌟 Criador simples da Zephyra carregado!")
    print("💡 Execute: create_zephyra_simple()")
