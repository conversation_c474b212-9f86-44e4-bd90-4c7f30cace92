# Script: verify_ue56_installation.py
# Verificação de instalação do Unreal Engine 5.6 para AURACRON
# Implementação completa baseada no checklist (linhas 47-92)

import unreal
import sys
import os

def verify_ue56_installation():
    """
    Verifica se a instalação do Unreal Engine é versão 5.6 ou superior
    Returns:
        bool: True se a versão é compatível, False caso contrário
    """
    try:
        # Obter versão do engine usando API oficial do UE5.6
        engine_version = unreal.SystemLibrary.get_engine_version()
        print(f"✅ Unreal Engine Version: {engine_version}")
        
        # Verificar se é versão 5.6 ou superior usando semantic versioning
        version_parts = engine_version.split('.')
        if len(version_parts) < 2:
            print(f"❌ Formato de versão inválido: {engine_version}")
            return False
            
        try:
            major = int(version_parts[0])
            minor = int(version_parts[1])
        except ValueError:
            print(f"❌ Não foi possível parsear a versão: {engine_version}")
            return False
        
        # Validação de versão: deve ser 5.6 ou superior
        if major < 5 or (major == 5 and minor < 6):
            print(f"❌ Versão {engine_version} não suportada. Requer UE 5.6+")
            return False
            
        print("✅ Versão do Unreal Engine compatível")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao verificar versão do UE: {e}")
        return False

def verify_python_api():
    """
    Verifica se a Python API do Unreal Engine está funcionando corretamente
    Returns:
        bool: True se a API está funcional, False caso contrário
    """
    try:
        # Verificar se Python API está disponível testando acesso ao diretório /Game/
        # Usando API oficial do UE5.6 EditorAssetLibrary
        api_test = unreal.EditorAssetLibrary.does_asset_exist('/Game/')
        print("✅ Python API do Unreal Engine funcionando")
        return True
    except Exception as e:
        print(f"❌ Python API não disponível: {e}")
        return False

def main():
    """
    Função principal que executa todas as verificações de pré-requisitos
    """
    print("🔍 Verificando pré-requisitos do AURACRON...")
    
    # Verificar instalação do UE5.6
    if not verify_ue56_installation():
        print("❌ Falha na verificação da versão do Unreal Engine")
        sys.exit(1)
        
    # Verificar Python API
    if not verify_python_api():
        print("❌ Falha na verificação da Python API")
        sys.exit(1)
        
    print("✅ Todos os pré-requisitos verificados com sucesso!")
    print("🎮 Sistema pronto para desenvolvimento do AURACRON")

if __name__ == "__main__":
    main()