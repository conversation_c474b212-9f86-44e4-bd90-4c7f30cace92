# 🎭 ZEPHYRA - REQUISITOS DE MESH 3D

## 📋 Especificações Técnicas

### **Formato de Arquivo**
- **Primário:** `.fbx` (recomendado)
- **Alternativo:** `.obj` (para testes)
- **Versão FBX:** 2020.3.4 ou superior

### **Requisitos de Modelagem**

#### **Geometria Base**
- **Polígonos:** 15,000 - 25,000 tris (LOD0)
- **Topologia:** Quad-based com bom edge flow
- **Escala:** Altura padrão de personagem UE (180cm)
- **Proporções:** Ligeiramente alongada (1.05x altura)

#### **LODs (Levels of Detail)**
- **LOD0:** 25,000 tris (máxima qualidade)
- **LOD1:** 15,000 tris (qualidade alta)
- **LOD2:** 8,000 tris (qualidade média)
- **LOD3:** 4,000 tris (qualidade baixa)

### **Rigging e Animação**

#### **Skeleton**
- **Compatível com:** UE5 Mannequin Skeleton
- **Bones extras:** Para cabelo etéreo e efeitos
- **IK Chains:** Braços, pernas, coluna
- **Facial Rig:** Para expressões dimensionais

#### **Skin Weights**
- **Máximo:** 4 influences por vértice
- **Qualidade:** Smooth deformation
- **Áreas críticas:** Juntas, face, cabelo

### **UV Mapping**

#### **UV Sets**
- **UV0:** Diffuse, Normal, Roughness
- **UV1:** Lightmaps (se necessário)
- **UV2:** Efeitos especiais prismais

#### **Resolução de Textura**
- **Corpo:** 2048x2048 (mínimo)
- **Face:** 1024x1024
- **Cabelo:** 1024x1024
- **Efeitos:** 512x512

### **Características Especiais da Zephyra**

#### **Aparência Etérea**
- **Translucidez:** Áreas semi-transparentes
- **Distorção:** Geometria para efeitos dimensionais
- **Cabelo:** Modelado para anti-gravidade
- **Olhos:** Geometria especial para reflexos dos Realms

#### **Marcas Dimensionais**
- **Sigilos:** Geometria separada para animação
- **Emissão:** Áreas para glow effects
- **Distorção:** Vértices para shader effects

### **Collision**

#### **Collision Mesh**
- **Tipo:** Simplified convex hulls
- **Complexidade:** Baixa (500-1000 tris)
- **Cobertura:** Corpo principal apenas
- **Exclusões:** Cabelo, efeitos, detalhes

### **Materiais e Shaders**

#### **Material Slots**
1. **Body_Prismal** - Corpo com efeitos prismais
2. **Face_Dimensional** - Face com olhos especiais
3. **Hair_Ethereal** - Cabelo etéreo
4. **Sigils_Animated** - Marcas dimensionais
5. **Effects_Glow** - Efeitos de brilho

#### **Shader Features Necessários**
- **Subsurface Scattering** - Para translucidez
- **Emission** - Para brilho prismal
- **Distortion** - Para efeitos dimensionais
- **Animated UVs** - Para sigilos pulsantes

### **Naming Convention**

#### **Arquivos**
- `SK_Zephyra_Body.fbx` - Mesh principal
- `SK_Zephyra_Hair.fbx` - Cabelo separado (se necessário)
- `SK_Zephyra_LOD1.fbx` - LOD 1
- `SK_Zephyra_LOD2.fbx` - LOD 2
- `SK_Zephyra_LOD3.fbx` - LOD 3

#### **Bones**
- Seguir convenção UE5 Mannequin
- Prefixo `b_` para bones
- Sufixo `_l` / `_r` para esquerda/direita

#### **Materials**
- `M_Zephyra_Body_Prismal`
- `M_Zephyra_Face_Dimensional`
- `M_Zephyra_Hair_Ethereal`
- `M_Zephyra_Sigils_Animated`

### **Checklist de Qualidade**

#### **Antes da Exportação**
- [ ] Geometria limpa (sem n-gons, faces sobrepostas)
- [ ] UVs sem overlaps (exceto onde intencional)
- [ ] Rigging testado e funcional
- [ ] Materiais aplicados corretamente
- [ ] Escala correta (180cm altura)
- [ ] Pivot no centro da base

#### **Após Importação no UE5**
- [ ] LODs funcionando corretamente
- [ ] Materiais aplicados
- [ ] Collision funcionando
- [ ] Animações de teste funcionais
- [ ] Performance aceitável

### **Referências Visuais**

#### **Inspiração de Design**
- Arquitetas místicas de fantasia
- Seres dimensionais etéreos
- Manipuladoras de realidade
- Estética prismal/cristalina

#### **Paleta de Cores**
- **Primária:** Roxo dimensional (#9933CC)
- **Secundária:** Azul prismal (#33CCFF)
- **Acentos:** Dourado trilhos (#FFCC33)

---

**Status:** 🔄 Aguardando Criação  
**Prioridade:** 🔥 Alta  
**Estimativa:** 2-3 semanas para mesh completo  
**Responsável:** Equipe de Arte 3D  
