#!/usr/bin/env python3
"""
TESTE SIMPLES - CRIAR ZEPHYRA
=============================

Script ultra-simplificado para testar criação da Zephyra
"""

import unreal

def test_create_zephyra():
    """Teste simples de criação"""
    try:
        print("🌟 === TESTE SIMPLES ZEPHYRA ===")
        
        # Verificar se estamos no UE
        try:
            editor_subsystem = unreal.get_editor_subsystem(unreal.UnrealEditorSubsystem)
            if editor_subsystem:
                print("✅ UnrealEditorSubsystem encontrado")
                
                world = editor_subsystem.get_editor_world()
                if world:
                    print(f"✅ Mundo encontrado: {world.get_name()}")
                else:
                    print("❌ Mundo não encontrado")
                    return False
            else:
                print("❌ UnrealEditorSubsystem não encontrado")
                return False
        except Exception as e:
            print(f"❌ Erro ao acessar subsistemas: {str(e)}")
            return False
        
        # Tentar criar ator simples
        try:
            print("🏗️ Criando ator da Zephyra...")
            
            # Criar ator usando API mais simples
            actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
                unreal.Actor,
                unreal.Vector(0, 0, 100)
            )
            
            if actor:
                actor.set_actor_label("Zephyra_TestActor")
                print(f"✅ Ator criado: {actor.get_actor_label()}")
                print(f"📍 Localização: {actor.get_actor_location()}")
                
                # Adicionar tags
                actor.tags = [
                    unreal.Name("Zephyra"),
                    unreal.Name("Test"),
                    unreal.Name("Champion")
                ]
                print("✅ Tags adicionadas")
                
                return True
            else:
                print("❌ Falha ao criar ator")
                return False
                
        except Exception as e:
            print(f"❌ Erro ao criar ator: {str(e)}")
            return False
            
    except Exception as e:
        print(f"❌ Erro geral: {str(e)}")
        return False

def main():
    """Função principal"""
    success = test_create_zephyra()
    if success:
        print("🎉 TESTE BEM-SUCEDIDO!")
        print("🌟 Zephyra Test Actor criado!")
        print("💡 Verifique o World Outliner")
    else:
        print("❌ TESTE FALHOU!")
    return success

# Executar
if __name__ == "__main__":
    main()
else:
    print("🧪 Teste simples da Zephyra carregado")
    print("💡 Execute: test_create_zephyra()")
