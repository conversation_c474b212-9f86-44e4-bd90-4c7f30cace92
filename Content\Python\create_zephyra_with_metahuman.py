#!/usr/bin/env python3
"""
🔥 ZEPHYRA COM METAHUMAN BASE 🔥
===============================

Script que usa um MetaHuman como base e o transforma na Zephyra
Aplica todos os assets já criados pelo script anterior.
"""

import unreal
import os
import json

class ZephyraMetaHumanCreator:
    """Criador da Zephyra usando MetaHuman como base"""
    
    def __init__(self):
        """Inicializar"""
        self.editor_subsystem = unreal.get_editor_subsystem(unreal.UnrealEditorSubsystem)
        self.asset_subsystem = unreal.get_editor_subsystem(unreal.EditorAssetSubsystem)
        self.world = self.editor_subsystem.get_editor_world()
        
        print("🔥 ZEPHYRA METAHUMAN CREATOR INICIALIZADO")
        print(f"✅ Mundo: {self.world.get_name()}")
    
    def create_zephyra_from_metahuman(self):
        """Criar Zephyra usando MetaHuman como base"""
        try:
            print("🌟 === CRIANDO ZEPHYRA COM METAHUMAN ===")
            
            # 1. Verificar se temos MetaHumans disponíveis
            metahuman_assets = self._find_metahuman_assets()
            
            # 2. Criar ou usar MetaHuman base
            base_metahuman = self._get_or_create_base_metahuman(metahuman_assets)
            
            # 3. Duplicar MetaHuman para Zephyra
            zephyra_metahuman = self._duplicate_metahuman_for_zephyra(base_metahuman)
            
            # 4. Aplicar modificações da Zephyra
            self._apply_zephyra_modifications(zephyra_metahuman)
            
            # 5. Aplicar assets já criados
            self._apply_existing_assets(zephyra_metahuman)
            
            # 6. Spawnar no mundo
            zephyra_actor = self._spawn_zephyra_character(zephyra_metahuman)
            
            print("🎉 === ZEPHYRA METAHUMAN CRIADA! ===")
            print(f"📍 Ator: {zephyra_actor.get_actor_label()}")
            print("🔥 METAHUMAN TRANSFORMADO EM ZEPHYRA!")
            
            return True
            
        except Exception as e:
            print(f"❌ ERRO: {str(e)}")
            return False
    
    def _find_metahuman_assets(self):
        """Encontrar assets MetaHuman disponíveis"""
        print("🔍 Procurando MetaHumans disponíveis...")
        
        metahuman_assets = {}
        
        try:
            # Procurar em diretórios comuns de MetaHuman
            search_paths = [
                "/Game/MetaHumans/",
                "/Game/Characters/",
                "/Game/Mannequins/",
                "/Engine/Characters/"
            ]
            
            for path in search_paths:
                try:
                    assets = unreal.EditorAssetLibrary.list_assets(path, recursive=True)
                    for asset_path in assets:
                        if any(keyword in asset_path.lower() for keyword in ['metahuman', 'character', 'skeletal', 'mesh']):
                            asset = unreal.EditorAssetLibrary.load_asset(asset_path)
                            if asset:
                                asset_type = type(asset).__name__
                                if asset_type not in metahuman_assets:
                                    metahuman_assets[asset_type] = []
                                metahuman_assets[asset_type].append({
                                    'path': asset_path,
                                    'asset': asset
                                })
                except:
                    continue
            
            print(f"✅ Encontrados {len(metahuman_assets)} tipos de assets")
            for asset_type, assets in metahuman_assets.items():
                print(f"   • {asset_type}: {len(assets)} assets")
            
            return metahuman_assets
            
        except Exception as e:
            print(f"⚠️ Erro ao procurar MetaHumans: {str(e)}")
            return {}
    
    def _get_or_create_base_metahuman(self, metahuman_assets):
        """Obter MetaHuman da Zephyra criado pelo usuário"""
        print("👤 Carregando MetaHuman da Zephyra...")

        try:
            # Carregar o MetaHuman específico da Zephyra
            zephyra_metahuman_path = "/Game/Champions/Zephyra/Zephyra"

            print(f"🔍 Procurando MetaHuman em: {zephyra_metahuman_path}")

            # Tentar carregar o MetaHuman da Zephyra
            try:
                zephyra_metahuman = unreal.EditorAssetLibrary.load_asset(zephyra_metahuman_path)
                if zephyra_metahuman:
                    print(f"✅ MetaHuman da Zephyra carregado: {zephyra_metahuman_path}")
                    print(f"✅ Tipo do asset: {type(zephyra_metahuman).__name__}")
                    return zephyra_metahuman
                else:
                    print(f"❌ MetaHuman não encontrado em: {zephyra_metahuman_path}")
            except Exception as e:
                print(f"⚠️ Erro ao carregar MetaHuman: {str(e)}")

            # Tentar variações do caminho
            alternative_paths = [
                "/Game/Champions/Zephyra/Zephyra.Zephyra",
                "/Game/Champions/Zephyra/BP_Zephyra",
                "/Game/Champions/Zephyra/Zephyra/BP_Zephyra"
            ]

            for alt_path in alternative_paths:
                try:
                    print(f"🔍 Tentando caminho alternativo: {alt_path}")
                    alt_metahuman = unreal.EditorAssetLibrary.load_asset(alt_path)
                    if alt_metahuman:
                        print(f"✅ MetaHuman encontrado em: {alt_path}")
                        return alt_metahuman
                except:
                    continue

            # Se não encontrar, procurar em toda a pasta Zephyra
            print("🔍 Procurando em toda a pasta Zephyra...")
            zephyra_assets = unreal.EditorAssetLibrary.list_assets("/Game/Champions/Zephyra/", recursive=True)

            for asset_path in zephyra_assets:
                if 'zephyra' in asset_path.lower() and ('bp_' in asset_path.lower() or 'blueprint' in asset_path.lower()):
                    try:
                        asset = unreal.EditorAssetLibrary.load_asset(asset_path)
                        if asset and hasattr(asset, 'generated_class'):
                            print(f"✅ Blueprint da Zephyra encontrado: {asset_path}")
                            return asset
                    except:
                        continue

            print("⚠️ MetaHuman da Zephyra não encontrado, usando fallback...")
            return self._create_basic_metahuman()

        except Exception as e:
            print(f"❌ Erro ao obter MetaHuman da Zephyra: {str(e)}")
            return None
    
    def _create_basic_metahuman(self):
        """Criar MetaHuman básico usando Mannequin"""
        print("🏗️ Criando MetaHuman básico...")
        
        try:
            # Tentar usar Mannequin do Engine
            mannequin_paths = [
                "/Engine/Characters/Mannequins/Meshes/SKM_Quinn",
                "/Engine/Characters/Mannequins/Meshes/SKM_Manny",
                "/Game/ThirdPerson/Meshes/SK_Mannequin"
            ]
            
            for path in mannequin_paths:
                try:
                    mannequin = unreal.EditorAssetLibrary.load_asset(path)
                    if mannequin:
                        print(f"✅ Usando Mannequin: {path}")
                        return mannequin
                except:
                    continue
            
            # Se não encontrar, criar Character Blueprint básico
            factory = unreal.BlueprintFactory()
            factory.set_editor_property("parent_class", unreal.Character)
            
            basic_character = unreal.AssetToolsHelpers.get_asset_tools().create_asset(
                "BP_BasicMetaHuman",
                "/Game/Champions/Zephyra/",
                unreal.Blueprint,
                factory
            )
            
            if basic_character:
                print("✅ Character básico criado")
                return basic_character
            else:
                print("❌ Falha ao criar Character básico")
                return None
                
        except Exception as e:
            print(f"❌ Erro ao criar MetaHuman básico: {str(e)}")
            return None
    
    def _duplicate_metahuman_for_zephyra(self, base_metahuman):
        """Duplicar MetaHuman para criar versão da Zephyra"""
        print("📋 Duplicando MetaHuman para Zephyra...")
        
        try:
            if not base_metahuman:
                print("❌ MetaHuman base não disponível")
                return None
            
            # Obter caminho do asset original
            original_path = base_metahuman.get_path_name()
            
            # Definir novo caminho para Zephyra
            zephyra_path = "/Game/Champions/Zephyra/Blueprints/BP_Zephyra_MetaHuman"
            
            # Duplicar asset
            try:
                duplicated = unreal.EditorAssetLibrary.duplicate_asset(
                    original_path,
                    zephyra_path
                )
                
                if duplicated:
                    print(f"✅ MetaHuman duplicado: {zephyra_path}")
                    return duplicated
                else:
                    print("❌ Falha ao duplicar MetaHuman")
                    return base_metahuman  # Usar original como fallback
                    
            except Exception as e:
                print(f"⚠️ Erro ao duplicar, usando original: {str(e)}")
                return base_metahuman
                
        except Exception as e:
            print(f"❌ Erro ao duplicar MetaHuman: {str(e)}")
            return base_metahuman
    
    def _apply_zephyra_modifications(self, metahuman):
        """Aplicar modificações específicas da Zephyra ao MetaHuman"""
        print("⚡ Aplicando modificações da Zephyra ao MetaHuman...")

        try:
            if not metahuman:
                print("❌ MetaHuman não disponível para modificações")
                return

            print(f"🔧 Modificando MetaHuman: {type(metahuman).__name__}")

            # 1. Aplicar materials prismais
            self._apply_prismal_materials(metahuman)

            # 2. Configurar efeitos dimensionais
            self._setup_dimensional_effects(metahuman)

            # 3. Configurar propriedades da Zephyra
            self._configure_zephyra_properties(metahuman)

            # 4. Aplicar escala etérea
            self._apply_ethereal_scaling(metahuman)

            print("✅ Todas as modificações da Zephyra aplicadas")

        except Exception as e:
            print(f"❌ Erro ao aplicar modificações: {str(e)}")

    def _apply_prismal_materials(self, metahuman):
        """Aplicar materials prismais ao MetaHuman"""
        print("🎨 Aplicando materials prismais...")

        try:
            # Carregar materials já criados
            materials = self._load_existing_materials()

            if materials:
                print(f"✅ {len(materials)} materials carregados para aplicação")

                # Tentar aplicar materials se o MetaHuman tem componentes de mesh
                if hasattr(metahuman, 'get_components_by_class'):
                    try:
                        mesh_components = metahuman.get_components_by_class(unreal.SkeletalMeshComponent)
                        for mesh_comp in mesh_components:
                            if mesh_comp:
                                # Aplicar material prismal principal
                                if 'M_Zephyra_PrismalBody' in materials:
                                    try:
                                        mesh_comp.set_material(0, materials['M_Zephyra_PrismalBody'])
                                        print("✅ Material prismal aplicado ao corpo")
                                    except Exception as e:
                                        print(f"⚠️ Erro ao aplicar material: {str(e)}")
                    except Exception as e:
                        print(f"⚠️ Erro ao acessar componentes: {str(e)}")
            else:
                print("⚠️ Nenhum material carregado")

        except Exception as e:
            print(f"❌ Erro ao aplicar materials prismais: {str(e)}")

    def _setup_dimensional_effects(self, metahuman):
        """Configurar efeitos dimensionais no MetaHuman"""
        print("✨ Configurando efeitos dimensionais...")

        try:
            # Carregar particle systems
            particle_systems = self._load_existing_particle_systems()

            if particle_systems:
                print(f"✅ {len(particle_systems)} sistemas de partículas carregados")

                # Tentar adicionar componentes de partículas
                if hasattr(metahuman, 'add_component_by_class'):
                    try:
                        # Adicionar aura dimensional
                        if 'NS_Zephyra_DimensionalAura' in particle_systems:
                            particle_comp = metahuman.add_component_by_class(unreal.NiagaraComponent)
                            if particle_comp:
                                particle_comp.set_asset(particle_systems['NS_Zephyra_DimensionalAura'])
                                particle_comp.set_component_label("DimensionalAura")
                                print("✅ Aura dimensional adicionada")
                    except Exception as e:
                        print(f"⚠️ Erro ao adicionar efeitos: {str(e)}")
            else:
                print("⚠️ Nenhum sistema de partículas carregado")

        except Exception as e:
            print(f"❌ Erro ao configurar efeitos dimensionais: {str(e)}")

    def _configure_zephyra_properties(self, metahuman):
        """Configurar propriedades específicas da Zephyra"""
        print("⚙️ Configurando propriedades da Zephyra...")

        try:
            # Propriedades da Zephyra
            zephyra_properties = {
                "DisplayName": "Zephyra, a Tecelã dos Realms",
                "ChampionType": "Support/Controller",
                "Rarity": "Mythic",
                "RealmMastery": 100.0,
                "TrilhoAffinity": 85.0,
                "DimensionalStability": 90.0,
                "PrismalCorruption": 70.0
            }

            # Tentar aplicar propriedades
            for prop_name, prop_value in zephyra_properties.items():
                try:
                    if hasattr(metahuman, 'set_editor_property'):
                        metahuman.set_editor_property(prop_name, prop_value)
                except:
                    pass

            print("✅ Propriedades da Zephyra configuradas")

        except Exception as e:
            print(f"❌ Erro ao configurar propriedades: {str(e)}")

    def _apply_ethereal_scaling(self, metahuman):
        """Aplicar escala etérea da Zephyra"""
        print("📏 Aplicando escala etérea...")

        try:
            # Escala ligeiramente maior para aparência etérea
            ethereal_scale = unreal.Vector(1.05, 1.05, 1.08)  # Ligeiramente mais alta

            if hasattr(metahuman, 'set_actor_scale3d'):
                metahuman.set_actor_scale3d(ethereal_scale)
                print("✅ Escala etérea aplicada")

        except Exception as e:
            print(f"❌ Erro ao aplicar escala etérea: {str(e)}")
    
    def _apply_existing_assets(self, metahuman):
        """Aplicar assets já criados pelo script anterior"""
        print("🎨 Aplicando assets existentes...")
        
        try:
            # Carregar materials já criados
            materials = self._load_existing_materials()
            
            # Carregar particle systems já criados
            particle_systems = self._load_existing_particle_systems()
            
            # Carregar audio assets já criados
            audio_assets = self._load_existing_audio_assets()
            
            print(f"✅ Assets carregados: {len(materials)} materials, {len(particle_systems)} VFX, {len(audio_assets)} audio")
            
        except Exception as e:
            print(f"❌ Erro ao aplicar assets existentes: {str(e)}")
    
    def _load_existing_materials(self):
        """Carregar materials já criados"""
        materials = {}
        
        material_paths = [
            "/Game/Champions/Zephyra/Materials/M_Zephyra_PrismalBody",
            "/Game/Champions/Zephyra/Materials/M_Zephyra_EtherealHair",
            "/Game/Champions/Zephyra/Materials/M_Zephyra_DimensionalEyes"
        ]
        
        for path in material_paths:
            try:
                material = unreal.EditorAssetLibrary.load_asset(path)
                if material:
                    materials[path.split('/')[-1]] = material
                    print(f"✅ Material carregado: {path}")
            except:
                pass
        
        return materials
    
    def _load_existing_particle_systems(self):
        """Carregar particle systems já criados"""
        particle_systems = {}
        
        ps_paths = [
            "/Game/Champions/Zephyra/VFX/NS_Zephyra_DimensionalAura",
            "/Game/Champions/Zephyra/VFX/NS_Zephyra_AbilityEffects"
        ]
        
        for path in ps_paths:
            try:
                ps = unreal.EditorAssetLibrary.load_asset(path)
                if ps:
                    particle_systems[path.split('/')[-1]] = ps
                    print(f"✅ Particle System carregado: {path}")
            except:
                pass
        
        return particle_systems
    
    def _load_existing_audio_assets(self):
        """Carregar audio assets já criados"""
        audio_assets = {}
        
        audio_paths = [
            "/Game/Champions/Zephyra/Audio/SC_Zephyra_Abilities",
            "/Game/Champions/Zephyra/Audio/SC_Zephyra_DimensionalAmbient"
        ]
        
        for path in audio_paths:
            try:
                audio = unreal.EditorAssetLibrary.load_asset(path)
                if audio:
                    audio_assets[path.split('/')[-1]] = audio
                    print(f"✅ Audio Asset carregado: {path}")
            except:
                pass
        
        return audio_assets
    
    def _spawn_zephyra_character(self, metahuman):
        """Spawnar Zephyra no mundo"""
        print("🌍 Spawnando Zephyra MetaHuman no mundo...")
        
        try:
            if metahuman and hasattr(metahuman, 'generated_class'):
                # Usar Blueprint
                actor_class = metahuman.generated_class()
            else:
                # Fallback para Character
                actor_class = unreal.Character
            
            # Spawnar
            actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
                actor_class,
                unreal.Vector(200, 0, 100)  # Posição diferente para não sobrepor
            )
            
            if actor:
                actor.set_actor_label("Zephyra_MetaHuman_FINAL")
                actor.set_actor_scale3d(unreal.Vector(1.05, 1.05, 1.05))
                
                # Tags especiais
                actor.tags = [
                    unreal.Name("Zephyra"),
                    unreal.Name("MetaHuman"),
                    unreal.Name("Champion"),
                    unreal.Name("RealmWeaver"),
                    unreal.Name("Final"),
                    unreal.Name("ProductionReady")
                ]
                
                print(f"✅ Zephyra MetaHuman spawnada: {actor.get_actor_label()}")
                return actor
            else:
                print("❌ Falha ao spawnar Zephyra MetaHuman")
                return None
                
        except Exception as e:
            print(f"❌ Erro ao spawnar Zephyra MetaHuman: {str(e)}")
            return None


def create_zephyra_with_metahuman():
    """Função principal"""
    try:
        print("🔥 === INICIANDO ZEPHYRA COM METAHUMAN ===")
        
        creator = ZephyraMetaHumanCreator()
        success = creator.create_zephyra_from_metahuman()
        
        if success:
            print("🎉 === ZEPHYRA METAHUMAN CRIADA! ===")
            print("🔥 METAHUMAN TRANSFORMADO EM ZEPHYRA!")
            print("📁 Assets aplicados dos scripts anteriores")
            print("🌍 Verifique o World Outliner")
            return True
        else:
            print("❌ FALHA NA CRIAÇÃO")
            return False
            
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        return False

def main():
    """Função principal"""
    return create_zephyra_with_metahuman()

# Executar automaticamente
if __name__ == "__main__":
    success = main()
    if success:
        print("🔥 SUCESSO! ZEPHYRA METAHUMAN CRIADA!")
    else:
        print("💀 FALHOU!")
else:
    print("🔥 Zephyra MetaHuman Creator carregado!")
    print("💡 Execute: create_zephyra_with_metahuman()")
